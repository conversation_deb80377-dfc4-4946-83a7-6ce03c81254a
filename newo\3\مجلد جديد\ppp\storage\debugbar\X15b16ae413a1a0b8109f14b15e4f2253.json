{"__meta": {"id": "X15b16ae413a1a0b8109f14b15e4f2253", "datetime": "2025-06-17 15:42:28", "utime": **********.897651, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174947.476776, "end": **********.897687, "duration": 1.4209110736846924, "duration_str": "1.42s", "measures": [{"label": "Booting", "start": 1750174947.476776, "relative_start": 0, "end": **********.735146, "relative_end": **********.735146, "duration": 1.2583701610565186, "duration_str": "1.26s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.735169, "relative_start": 1.2583930492401123, "end": **********.89769, "relative_end": 3.0994415283203125e-06, "duration": 0.1625211238861084, "duration_str": "163ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44967712, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.03248, "accumulated_duration_str": "32.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.829969, "duration": 0.03074, "duration_str": "30.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.643}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.873261, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 94.643, "width_percent": 5.357}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1938735581 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1938735581\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-514802352 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-514802352\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1498244134 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1498244134\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1126633426 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750174938454%7C11%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjBrK296aHVnM25vd1V1L3NTc2wyNlE9PSIsInZhbHVlIjoiR1UzbEtqNjliVm1FTXk3MHhpNmhiNDVmSlQ3N01BQ3pUSkU3MnBBdnVwOThXOWttQ3JFZ2QyK0kyeE8wYmVGT05KQ1JtU0UvTlJ3V1NnUTA0RjhqNlovZ2hHUUhraktaR20ydFNIQW5rb0xlTjRVblFyb01BSElLdGVHYU9UYlBqRzl2a25CQjU1NkNiYm9pNk5VZVpZV0t4TDRYKzZxSjY2eUU1QjZzTTJmSHJyS1BLRWJVQnVzSXpPNi9QMFBaa1RJWFdqSzA0N2pnNlpmTUtaUUJnY3JjVGtOa2wwUWJBODl4Tk50Unhzaks5TFBHUG5acWJwazZWSVFLU2kyK1JTUEw4eXh2LzRmTzI1Ly9ObW1GZGdLSTI4UUNCRG5yVjcxOTI1UlI0NUFBM1VzK0EycjFWeW94dk9iMm9iVDFxUVl3cko1OGVadmk0VVBXK2RPNVlmcVkyMllqb01iUnFOKzZtT2dvWHJXWUpJLzAyTjFibWMycmZwdThzeE5FQWhScVNmNFJISFV4UXM0WldxU2pUc3NNcEQvRFF1bVNrU3Uva2twcmIzMmMrZGN3aitKREdxOGFNMkZQRkRFVG43YUhpZzE3TngwczNUekN0YlJmQm50ZmZWUkRwV29yYjVCMkFsSHZMbGhFQU9xc0hEb3g0Z2EybnNnSlJRMXIiLCJtYWMiOiIxNjA5YWI2ZWVkYzI1NDU2YzRjZTIzM2JkYmRmOGJkMjA3YWMxNTFkNGZhNDQ5Y2NjNmY1NjI3NDBiZDQxYzliIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlhGQUVPWmZjejIvZm1pSndYczQvZmc9PSIsInZhbHVlIjoiUSs2aFU5d1NlbktmVFc5VFliMC9UamYvM1Y0bmNPSFZIYS9EdllVQnEwM1g1RFlzMHhEVVM1dzZSUnpscDlMZi93MzlaTWx0SkZMcnJ3THp2cjV6UUU5bDA4OHJ3RUpqbjI1YUgwN1NTSFhhUHB0alZudVc4WEFDWUFTcnBwd0YwTExXQWJWOGpheXF3WUszQ1g0T0lwazU5TkI0REMwZnFkY0xRcXlCMXM2SHZoOHhINURPcHpMMUpva1ArL3ZoTlR4RCtuUnZjTVQxbjQzS3Vza0JkVXVQcmxOSnpUSjlRVmpRRWFEVjA1TzRDNFVFVE16T2J0ekcwN09ndTFBL203Z3NQalRwdFhjUDlQdm1SOTg3cEdIaFBNc0MzS2dhVDRsQU1HLzNicEtVQ2t5QTY4c3hUYkF5RE5lZkRlMERsdU9pRVJxVWhhQk1tUFlGVWhWdUE3WVBnMUVOZ1NlSDNaWmtISTRWa3N1Q09VZTN2cVNEV0xvZEdaYU5wWDlWU0E4Z3lucmVGKzJnd0sycndZU2Y2UmRIdDI3STY2dFhGTnAvYjQ5aEJPeXpvMjB6aHRGV3lkRkpCSFlRczN6SmNqeDJHc1pQOHU5WlJhYkIvN2ZsQllncjZOQ1g2Q0o2eE9DbjNjbDAyZWJDR3lMSkExYkUwUzJSaCtwZDBMWVYiLCJtYWMiOiJmNTU2MmU2NzQ2NGQyMDgzM2M3OGRhNzg4ZGQxNDM4OTU3NDllMTA2MGE3ODcwZjkyZmI3NTIyZmY2ZjdjZDZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1126633426\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1753417238 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1753417238\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-914214201 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:42:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxxZnJBY2RhWU12QzJCdWtudGpOZ3c9PSIsInZhbHVlIjoiekNWdHZ3a3BUc1l2MFpnSVFWbnJaSkxXR0FrZ3lLa2pzSmlnTFduL3RqSVdYMmhSYmRtTmcvbEJmSDBBeGVWbG1McWxOS3JCZlNzbklIRTdDbW5qOVk1LzFnUkxIZjFLRStjMzJpTE80K3BBdm0yYmY4cks0bFpicTBxWVlDd3MvVVJZVnNGcEdLTkQ0RGh2ck5rTHFwMVlvODdGZlE3NWNXSzc2Q3dMWmxBMkN6ejA4OCt1cGI4Y21JTnQ5ZS9Edmx3MUs4eGo0alpCS1ZOMlI0ZDdwMUpXcTl3a0d0Rk50TXZEMy85RGhKYmhuSjkxbmVlRVJrR1hPQS9PUTd6QXAxSHFJcmJsZEVxUmp2KzdIaDMwSlNFSlJDdEJIQlM4L3ZRdFpsU1g4THdnbEVKVUZYK0xiV3dHbnNqVGpxRjdiK3RxSnVwbGJwWVh2d0dwSHR5L3ZIOUI4QjhuTnBkOEZWRUhTMHBuOEwvNm10Q1RWUDdKMitYWCt0YndFajBEMkVMWGE4dWF2RVZ6RTJleHZHSHJvZXAvV3U0UEVqUitYa0MwTTlTTHhQOHpweCt5QVlyV0NyNUQyNkNrdlZTR05EREFVSXIrUzdxMWdkNWdTQVFKenNZZE9WTGlwQWRqdkprVlRFVXVjenlEb2ZmWGRpZEZlSFpZSzhzWEN0b2siLCJtYWMiOiI0ZWVjNmU3ODhjZjM0YmY5MDRlYjY4MjJlMmU3NDM2OTgwZjAzZGE3MGNkM2NkN2FiZGYwYjA5MTk0ZTgzZWU0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjlydkQ4WXpwY2tqeXVvSTFraEVNOEE9PSIsInZhbHVlIjoiRVB4SEszWmJvK1JoQ3NyVTJpbWh0T0xNREQ0RmFzVm5JTXM5MTRWVjhMRDk2VElBMzJMNjdNMGxWRnM5RHNQb3c5QVMycUhmWEpQUnNIWFlGUXQ5bGwrZXZUYW9hNENBenFrM2NnU1FHT2hURndENmVkYTZEK0RXUFlScUV0SC9WUGdtdEZSeEswelYzTkFjR1NJckhxdXFEdjkzY0x0MjJSbGQ5M1FRL2JMQ2RRNlZMTmZyVndXMno4NzVadnIzSVRkcUhnZUlsN2x1L0FETGlRWnZaU2lnelVObUppV0M5dTNqaGdodlZTQTM5R2xzUEhOM0c0cVcxWi93Y2l5ZlRRdzJzdENYb2FPRkxadUZrVk9zVlZadGZpMGFCQURDOW9aTzZ3QWZwV0hBUGEvbnFVenNiZWZNZzd1MW1Ba3c5V0lWVlZXZHBmcEtCY0E2RUFEMzZHSkVzblh1ZlpCSTMvb2lmMlNzWWsvRDBlTzJzcXhoMThHWlhqNDFrYWlCN2grcGVBbVpUR1h2SUVEL0hUbDB3VXp3NFRvN2NzN205Y2cxYmhQT1BDL1g1ZDg4ZnBPVkhZQUlGVk44NzJ2TSt6dW9kbjQ2M0RqSUovcmJjS1FDZFZHWGJSRm54bTE3eDFsMHVyOVlXMkhCc2ttZE9WRVR3TFYzZ0djR0t1T3QiLCJtYWMiOiJiMGJmMThiMGExN2ZhNTE0ZjhmZmFkYmRmNTgyYzA0NTcxMTJiNzkwYzhiYjFhZDRiNGJhMDZhOTQyZWY3MmIyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxxZnJBY2RhWU12QzJCdWtudGpOZ3c9PSIsInZhbHVlIjoiekNWdHZ3a3BUc1l2MFpnSVFWbnJaSkxXR0FrZ3lLa2pzSmlnTFduL3RqSVdYMmhSYmRtTmcvbEJmSDBBeGVWbG1McWxOS3JCZlNzbklIRTdDbW5qOVk1LzFnUkxIZjFLRStjMzJpTE80K3BBdm0yYmY4cks0bFpicTBxWVlDd3MvVVJZVnNGcEdLTkQ0RGh2ck5rTHFwMVlvODdGZlE3NWNXSzc2Q3dMWmxBMkN6ejA4OCt1cGI4Y21JTnQ5ZS9Edmx3MUs4eGo0alpCS1ZOMlI0ZDdwMUpXcTl3a0d0Rk50TXZEMy85RGhKYmhuSjkxbmVlRVJrR1hPQS9PUTd6QXAxSHFJcmJsZEVxUmp2KzdIaDMwSlNFSlJDdEJIQlM4L3ZRdFpsU1g4THdnbEVKVUZYK0xiV3dHbnNqVGpxRjdiK3RxSnVwbGJwWVh2d0dwSHR5L3ZIOUI4QjhuTnBkOEZWRUhTMHBuOEwvNm10Q1RWUDdKMitYWCt0YndFajBEMkVMWGE4dWF2RVZ6RTJleHZHSHJvZXAvV3U0UEVqUitYa0MwTTlTTHhQOHpweCt5QVlyV0NyNUQyNkNrdlZTR05EREFVSXIrUzdxMWdkNWdTQVFKenNZZE9WTGlwQWRqdkprVlRFVXVjenlEb2ZmWGRpZEZlSFpZSzhzWEN0b2siLCJtYWMiOiI0ZWVjNmU3ODhjZjM0YmY5MDRlYjY4MjJlMmU3NDM2OTgwZjAzZGE3MGNkM2NkN2FiZGYwYjA5MTk0ZTgzZWU0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjlydkQ4WXpwY2tqeXVvSTFraEVNOEE9PSIsInZhbHVlIjoiRVB4SEszWmJvK1JoQ3NyVTJpbWh0T0xNREQ0RmFzVm5JTXM5MTRWVjhMRDk2VElBMzJMNjdNMGxWRnM5RHNQb3c5QVMycUhmWEpQUnNIWFlGUXQ5bGwrZXZUYW9hNENBenFrM2NnU1FHT2hURndENmVkYTZEK0RXUFlScUV0SC9WUGdtdEZSeEswelYzTkFjR1NJckhxdXFEdjkzY0x0MjJSbGQ5M1FRL2JMQ2RRNlZMTmZyVndXMno4NzVadnIzSVRkcUhnZUlsN2x1L0FETGlRWnZaU2lnelVObUppV0M5dTNqaGdodlZTQTM5R2xzUEhOM0c0cVcxWi93Y2l5ZlRRdzJzdENYb2FPRkxadUZrVk9zVlZadGZpMGFCQURDOW9aTzZ3QWZwV0hBUGEvbnFVenNiZWZNZzd1MW1Ba3c5V0lWVlZXZHBmcEtCY0E2RUFEMzZHSkVzblh1ZlpCSTMvb2lmMlNzWWsvRDBlTzJzcXhoMThHWlhqNDFrYWlCN2grcGVBbVpUR1h2SUVEL0hUbDB3VXp3NFRvN2NzN205Y2cxYmhQT1BDL1g1ZDg4ZnBPVkhZQUlGVk44NzJ2TSt6dW9kbjQ2M0RqSUovcmJjS1FDZFZHWGJSRm54bTE3eDFsMHVyOVlXMkhCc2ttZE9WRVR3TFYzZ0djR0t1T3QiLCJtYWMiOiJiMGJmMThiMGExN2ZhNTE0ZjhmZmFkYmRmNTgyYzA0NTcxMTJiNzkwYzhiYjFhZDRiNGJhMDZhOTQyZWY3MmIyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-914214201\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-368862426 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-368862426\", {\"maxDepth\":0})</script>\n"}}