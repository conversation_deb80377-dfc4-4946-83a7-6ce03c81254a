{"__meta": {"id": "Xb7189dec31703da1a250aee3bae018a9", "datetime": "2025-06-17 15:42:33", "utime": **********.742278, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174952.246803, "end": **********.742326, "duration": 1.4955229759216309, "duration_str": "1.5s", "measures": [{"label": "Booting", "start": 1750174952.246803, "relative_start": 0, "end": **********.436364, "relative_end": **********.436364, "duration": 1.189560890197754, "duration_str": "1.19s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.436424, "relative_start": 1.1896209716796875, "end": **********.742331, "relative_end": 5.0067901611328125e-06, "duration": 0.3059070110321045, "duration_str": "306ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49205496, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.03553, "accumulated_duration_str": "35.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.557255, "duration": 0.022670000000000003, "duration_str": "22.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 63.805}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6118422, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 63.805, "width_percent": 3.293}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.668701, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 67.098, "width_percent": 4.419}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.679423, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 71.517, "width_percent": 5.348}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.697709, "duration": 0.00435, "duration_str": "4.35ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 76.865, "width_percent": 12.243}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.712183, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 89.108, "width_percent": 10.892}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1156721056 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1156721056\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.694143, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1848498094 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1848498094\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-210010325 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-210010325\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1364133409 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1364133409\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750174938454%7C11%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtWeVZoUkNUbWtLY3RuNjhXaEtTVFE9PSIsInZhbHVlIjoiQ1ZIMDU3ejZSWHE0cmk4UmdobnJRbUxoSUxMZnFCVXNXMWNrZGJlbGd6QkdkdkZkN0lPcEdtc2tpRCtJOEo4N0tKZDBKaURrY1NCdkhnVnlPL3ZjcmtJVXVRYVhLMTZEckNNbmRFU3NjbmRTaGptT3oybzhLbUlPR3NWNTcrYmxvTE01M081T2l3ajR2aUtERVgvYWJna0NHSUxwMTAzYzNGK1Z0OHh3c3lkbkxWcDVqTWxqVGt6MDhXc2wybFdxcGF4L2p3aFNud2ZyRkJZeEc5NzJjWmRRbDZ2djFVQXU0V3I1SkVlUzRHOEhCc1BuNzBKcUhtcHJrNjhaQ1ppUW13K014b2VVcUxZc1l1RmYzdTV5VXNRcStDa3ozQnl1WWtoa0orNjQ0YWNNN1ErN1VPYlNWNGFJckg0T2JaK2pwbWswOE1yZjNSMlE0aDY1elBXS20rRWRpVzlQV1V0YW9IZ3BLQllVQ2lTN2k5WW55VS9wTTcrajRrN2c0clk0cXNIZThVZllUTWdNY1hoTTNXaVZCcjkzZlJJTnIrTExVRnpjaWNBTzc3SlBad3RSZTNyRUFOQkREeGFZYnZLY1pLUlFkb0Qza0R6TkZlWUxxbEg2NXpBaFFTczJPNFYzNXo2dnZsV3NjeDh2Lzcrd2VHZ1FsUFdJS1BYVE5hMHgiLCJtYWMiOiJhZjcxNDBlNjQxNjRjZDVmNTk3ODBkZmU0NjI4ZjM0MzNlZGVlYTAxYzAzOGFiMzFkOWFkYjMzMTYxN2FlZjkyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImZhUlYxWnBtNHlQa3dyWEwrQzBDSUE9PSIsInZhbHVlIjoiYmFMUCtqTVNLblRRZVhmYUMyeTlCckpUaVhoYVVINUJhNVQzSUNWeWtrclFRNGVKOXVZS3JXc0xZeEl2dm84bjlnOUQ5VlJuOXp5OXVEbVM1SWFqV2hqM3NZUXdhUkxHSjljMWF4UDY4WUxYVDhGK2E3cmFURHhnWmd5RXhVSU4zWGRmdWJ2TlhyNG1iR3ludjVjQnI3cjJ5UXZoS3E0VDNNSkY0VVNNRGlNSk0rMGlSUThZS3B1dmR2WWhZZEpiL2x0Zklvc3hzandGa3gzRkVpbHZGZFZJZUZVL3IyRmxJWHdQY2tqdGErSHJjalU4SGxySUFqRG5zSmFQeW96Y0pTWG84d0g5Yi9KaXUvc1RadmZvS295S21zVWh6VS9zMG1GS1M1LzNacG5tSW9JTlhGMUxDckc1VGxDNjgvRXlCOTZIaW8wcERHMVFmMGxpMVdldEdPVjV2aXhTd1U3ajEyeUJLRC91cjBCeW1QL3V6RmsyVXk1bVNsMDJIdUhMYTU0eXA1R2JJUm9KMm5EVERiOG1vQWt6M3Vuck1Vcys4aWk5cXE0SENseTFmM0k2T1NrQU4weFJEaFc3cXVHTXdlR3NweFBSbVBtRGpsTzJtTVgwc2FSbGI1cHFJNFgra3JUdmFUdlI3VVFGNzZPbkVPVGIxelZSWmtucTdxWlIiLCJtYWMiOiI4YzQyZmY0MDhkYzc2MGQ4MTUwMDI4YjhmYjFlZjYxOWUxNzliYzI1MjA5ODUxOTI1Yzk4ZjI3Mzc1YjdiMTAzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-629472099 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-629472099\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-184629818 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:42:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InAvMWFQUUVhUFpoR0dMMlE0SFgzUXc9PSIsInZhbHVlIjoiRGVFNExZMjg3VXdiMmkxQWcvcU9mc0VHZ1QyMEFYSCt0Q09abFlIV3loN0h5SnMzTlpVQVJGNjYwbGg0YTROUjFpdkRtSG9kdFlpVlhQeXFxVlZWVi9zUG12T2pjT2paNjNTNFlKWktuU3AxYmRZaHBOUTIyM0kvSmw2OHpRSTc2a1BkZnFzUXF5U0c2cUoyVkNOeEdhTUZCb2diZ05RdDJaRUFxcXNHUXowVzZlUm1wTHRNaWFNc2RxVEEvUFJJZzJGVFhVYXNVZ3lNQjRlZkRNK0lOeWs4eStHRzNtVW5XSUpXMkcyaE52QldPWW9qenFBcnFyMGhhUkR4cXdBMS9xZkJSRDYvWWNrL3hLVCtFSVRhV1AvTU5KRnRNcitjR3NSRzVYL3VhbHVtdjQxd0FrYWpZb1I3OXRhNmNnQlB6NUEzMmV2blZna0N4NXI0T0xNT01lNmxGU3lUcWhoMmVzaDZjVFYxM213WElldDlINDIvbWJMcWJDTmRNMTN5eXFiZWV3K1ZsQTdGYU9ZbTZGZSs1a2ZpS21OWXVWMEZwRG1YemRWY1g1UEovcS9oQ240R2ZvSHVzV3VKMDY2eVFEQTlJMlN2am9tejVzOHVlWjIwSmttcVFhajFZQ3lzNzB5bnc2V29TckpLUjFDcDVKWVJtMGttL0NBNWltejkiLCJtYWMiOiIwYmZjODE4MDc2YmE0NGE5NDJhM2ExNGRkYjBkODM2MWE5OGFhMjAwMWJiNTIyOGQ5Mzk4MDAyYjNhZmEyZGY3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Iml4NHhkUm1vSHJlK2w1MUJEUGhENXc9PSIsInZhbHVlIjoiQWxhUVA4QnAvdWJzZG1zK1ZXTUo5WndIbC9URHd3M1kzTlE5eUhtKzhxVDcvZlNJd0VCODMwaGZHczBiaFFjeHBIaTRBNnlvQ1NGV3FCK1FoWjRuTmx6ZFNBd3lTUHBFVkY0bGZIbytzSzBnZ0ZJZndIZEV1Z0RlZ3VQQzlrK3BkS0FuTUtkNm9Ec2F5b3pXeGY4SUpWZ0tXTzROWHFudlRpY0dXczJPek5jMWhTVkxXSjFWTCtJTXBXSjlNS0RkcnpEdjBqNTloMTUyU2hYVUtiVy9QODdPMDVyNHJ0SXhYZGhZc2xRMTI3aERRdko0cXFmTjYxZU03YmY2THI5K29IQk5CZzJ1WWN3ckJFbEEyR1g0TVN0Vjg0SStrR3VKeTg3ZTVhb2tDeE5GWUxacTQ2UUZOVlNlS2Z6a2NaeUR5SGc5VXg2VUVQUEgxZmQxVkRoSjFrR0Jzc3JuWXE2bkc2VUdrM3J0V05hR1BKWWY3NWVnM0ZQejhKckdBZFQ4L2d0UGVRVVRvbHI3NU9EcDF5bWxFTXVwOWpGazlwSGhtOVdSQTlMZW1GVWdQZllvM0dUYU9FNjl2dU1tZjRwK3ZxYmoybmZwbllGWW9lMStndjBIcFAvK0xFd24rdDJXaGFoRWNMQzdRUkdOc09qRUc2NGJQQS9HUlBkZVpLek0iLCJtYWMiOiI5NzUyMWE0NDcxNzIyNTE0NjEzZWFjNDkyNDliYWFjZjVkNDkzNjlkNjRhNDIyNGNjZDQyZWRlMzliODZhMDc1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InAvMWFQUUVhUFpoR0dMMlE0SFgzUXc9PSIsInZhbHVlIjoiRGVFNExZMjg3VXdiMmkxQWcvcU9mc0VHZ1QyMEFYSCt0Q09abFlIV3loN0h5SnMzTlpVQVJGNjYwbGg0YTROUjFpdkRtSG9kdFlpVlhQeXFxVlZWVi9zUG12T2pjT2paNjNTNFlKWktuU3AxYmRZaHBOUTIyM0kvSmw2OHpRSTc2a1BkZnFzUXF5U0c2cUoyVkNOeEdhTUZCb2diZ05RdDJaRUFxcXNHUXowVzZlUm1wTHRNaWFNc2RxVEEvUFJJZzJGVFhVYXNVZ3lNQjRlZkRNK0lOeWs4eStHRzNtVW5XSUpXMkcyaE52QldPWW9qenFBcnFyMGhhUkR4cXdBMS9xZkJSRDYvWWNrL3hLVCtFSVRhV1AvTU5KRnRNcitjR3NSRzVYL3VhbHVtdjQxd0FrYWpZb1I3OXRhNmNnQlB6NUEzMmV2blZna0N4NXI0T0xNT01lNmxGU3lUcWhoMmVzaDZjVFYxM213WElldDlINDIvbWJMcWJDTmRNMTN5eXFiZWV3K1ZsQTdGYU9ZbTZGZSs1a2ZpS21OWXVWMEZwRG1YemRWY1g1UEovcS9oQ240R2ZvSHVzV3VKMDY2eVFEQTlJMlN2am9tejVzOHVlWjIwSmttcVFhajFZQ3lzNzB5bnc2V29TckpLUjFDcDVKWVJtMGttL0NBNWltejkiLCJtYWMiOiIwYmZjODE4MDc2YmE0NGE5NDJhM2ExNGRkYjBkODM2MWE5OGFhMjAwMWJiNTIyOGQ5Mzk4MDAyYjNhZmEyZGY3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Iml4NHhkUm1vSHJlK2w1MUJEUGhENXc9PSIsInZhbHVlIjoiQWxhUVA4QnAvdWJzZG1zK1ZXTUo5WndIbC9URHd3M1kzTlE5eUhtKzhxVDcvZlNJd0VCODMwaGZHczBiaFFjeHBIaTRBNnlvQ1NGV3FCK1FoWjRuTmx6ZFNBd3lTUHBFVkY0bGZIbytzSzBnZ0ZJZndIZEV1Z0RlZ3VQQzlrK3BkS0FuTUtkNm9Ec2F5b3pXeGY4SUpWZ0tXTzROWHFudlRpY0dXczJPek5jMWhTVkxXSjFWTCtJTXBXSjlNS0RkcnpEdjBqNTloMTUyU2hYVUtiVy9QODdPMDVyNHJ0SXhYZGhZc2xRMTI3aERRdko0cXFmTjYxZU03YmY2THI5K29IQk5CZzJ1WWN3ckJFbEEyR1g0TVN0Vjg0SStrR3VKeTg3ZTVhb2tDeE5GWUxacTQ2UUZOVlNlS2Z6a2NaeUR5SGc5VXg2VUVQUEgxZmQxVkRoSjFrR0Jzc3JuWXE2bkc2VUdrM3J0V05hR1BKWWY3NWVnM0ZQejhKckdBZFQ4L2d0UGVRVVRvbHI3NU9EcDF5bWxFTXVwOWpGazlwSGhtOVdSQTlMZW1GVWdQZllvM0dUYU9FNjl2dU1tZjRwK3ZxYmoybmZwbllGWW9lMStndjBIcFAvK0xFd24rdDJXaGFoRWNMQzdRUkdOc09qRUc2NGJQQS9HUlBkZVpLek0iLCJtYWMiOiI5NzUyMWE0NDcxNzIyNTE0NjEzZWFjNDkyNDliYWFjZjVkNDkzNjlkNjRhNDIyNGNjZDQyZWRlMzliODZhMDc1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-184629818\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1960653845 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1960653845\", {\"maxDepth\":0})</script>\n"}}