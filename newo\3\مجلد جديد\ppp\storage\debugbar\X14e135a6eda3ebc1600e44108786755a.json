{"__meta": {"id": "X14e135a6eda3ebc1600e44108786755a", "datetime": "2025-06-17 15:43:04", "utime": **********.53512, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174983.097233, "end": **********.535155, "duration": 1.4379220008850098, "duration_str": "1.44s", "measures": [{"label": "Booting", "start": 1750174983.097233, "relative_start": 0, "end": **********.395805, "relative_end": **********.395805, "duration": 1.2985718250274658, "duration_str": "1.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.395826, "relative_start": 1.2985930442810059, "end": **********.535159, "relative_end": 4.0531158447265625e-06, "duration": 0.13933300971984863, "duration_str": "139ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44963488, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0208, "accumulated_duration_str": "20.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.486521, "duration": 0.01961, "duration_str": "19.61ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.279}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.515615, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 94.279, "width_percent": 5.721}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:1 [\n  8 => array:9 [\n    \"name\" => \"tea-شاهي\"\n    \"quantity\" => 1\n    \"price\" => \"2.00\"\n    \"id\" => \"8\"\n    \"tax\" => 0\n    \"subtotal\" => 2.0\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1560270161 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1560270161\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1374626929 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1374626929\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750174938454%7C11%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNPa296M3ZmK09kQnVzbitrcUR5bUE9PSIsInZhbHVlIjoiR2c5RE5SN0tmUDFqazB0N2IxOWpyVjBEcERQcnRSNnFlN3hDSTE2cHdlQUpjMTFNaHprZjBTNDRyUFJMZ2RmVCtnNEJzbVBjenNmZWtHdS9xcTJlQjJzdVF4RVFLWHdaT3dJTnQwNHdqcjR4UXJJd25rQnc3ZXB4L2RtSXIwLzVzcVFaYUJudmg3MUZ1TGR2WHlIWXBVdjJrTXVpK1RHS1B6N0VTT2x3K0c1bGkwS1U3TGU0ampXL1Vhb3JVVTFFQlVVSEF0ZFFDVlY0QkJwOTVET1BYeWdubXBVWmdXZ2lqaW9GME9rOERRcjhwcGg0R2NhUEUxbW5JUnF2UmVValhLNkZOdENib3ZoLy85YkRVaDFaRmU4cHBOTHYxWDdHUUFheXptQ1U1TWVESUtuUFhEcWhYVmVxcEFlZnQyM3pyYzNseHhvdVFOTGlSUmd0Q05sMjdIbmRwMWdPK3R6M1Y0TndMdE9XMCtGeG1sVFJMdmlsWWs0OUFQdHY2Tm91OUVqdzRxME9Ca043WnN5TzlsdHBzRGJnMlhBRUo5bDE3ZjVsM3dIdmZMQmZIcWNQamVaSWluZ2RGZDlFOUV6c3VIZGNpa2F3L3FqK0RpeUZIUk16cG1yZHlkczhKUU1YVXNiSXpsaWsrNXVWSWhiZ3lQT21PbUlPaDgveFJFMW4iLCJtYWMiOiI4YzVkYmMzMjhlODU1MjY0YzUwZjY5MTVkZGUyYzQ2ZTBiN2U1Nzk4Y2NlZDg2OTQ4MTYyNDJkMTBiZjI2NTNhIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlVyb1lZZXlFN1Z0NXRPYnFGTHJobXc9PSIsInZhbHVlIjoiMDdoZWhFTE1ib0M4NEdFcmFpYmhCOG4xYjI0NDNlcjM2eTBpSHMxeE9xT3RKQjB5LzBIM0lIRmdkS0hjK3FNYUVXdlJ4NlJpWlJIZVlXN2tVc2hCdUhXU1lJQTVsODE1RXJvNk5TeTlzUnhjK3VsM1JmWmo0cDJnbGdXV0wyVFZuaEdaT1IzYUtyTmk3WkE5NlNRdnhLSW5Xd2JNNC9KcVBnWENiTDJMV3JFQ2RYazRESHI5dVd1MmF3aHZHUytCdDU4eC9CU1Z6ZHowM2FtVDlITkRBUkw1Z09KTVBQVGIzck5kNWo0b3pBTk4rMDQ5alFwVTc4WElRMkF6TFpUeklZL3JIMTlWSGVoL1dQTStXU2xlWDJ2OHNKZThRWVZ4S2wrb09PRHZ2TlprdThFSUVDblJxUFpTeTVaZXdYV0trbC9EVTQ0ZitEMGR5YWR5REJWK2dlbnA2M0RRTUgvRmdZOFJwYnV3MXB1YnloZzFXWEduWVovRGNyejlDMDJGL0doTFZMZk44dU8xa1FEUWZtb0srKzNDYjBTVklCUlJWY1ROMklXOXVRTWlkdEVzVmJhYUF1cEpYM2IyWU16cGFod1lmU1BTR21kZkE3TXZsd3JoUHpQcFE5L2xmWjJJSFk5Wng3YVhENXozN3RmTkoxRWNMY3Vrb2hHWHhSWWQiLCJtYWMiOiJmNDA1NGFlZTNjNGNlYzhlNjc4YmM3ZmQ1ODZkNDg0ZmI3YzZmNDNiNWJmNDQxNGRmOGYyZTZjZTEyOGQyZjgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1145675170 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1145675170\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:43:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZPRlZXTzlFWkxsdGJnRTMrVG1sRUE9PSIsInZhbHVlIjoiOFdTRDlocXpWVHhFT3ZjVjR0T1loRXZhZ2dGUi96dHlheUVkLzFtb0x1TVhkNXFYMDNwRHlyVkp1MnM0MERHdmRZYXdaNGhqN3YwUGx5N1h0aUZSSmNPRityOENaMjZydm1YVG5VQ3FTcVM3UUpjQUxQM3ZRdnRVR0M5cXdhZ3RITGJQZExVdzk2ZVFZQ1BOMXB1dDJvRFNlRGVqbkFjelNFTXpZeUR2VzBnZ0F0dHZZZjYxbmVSRlVjb0dRRy91Rjh0QWVYUHJ5RWFvQjVrY2M0eDByN0MrTmRLRTZhYk9SdWVkQURoSG5vcHBqRERkU0U5blIxdFB4SmViYUROMXZrWWVTcnI3WXRQd3kzWjBOcXBhUStaTXN3dmRZQnRJRmdIRHhDcGp3aFVBNi9PL0g1dVRzQnQxWGRLb2FFbnp2aDF4RE1CUVB5aVFOTFdxeXYzNjRkUGtWa0tqTU5GZzY3VGI0ZVJRaVYyNFJxY1hUeXlHSzhjUldHZ1ZnNEdEZnJpcnpDM1FZSkduV2FiUnZsNGhNeDVhMVhoLzQ1b2pjWXhkbFFoWDJ1aldhNW9VcjdOckN3V3NBWnpTaTM0MTJVSU1NWHY2bmErOFBWUjRGVUhHZW1FaUpLbVN3eTBLSHZMaEs5blZXR2FLcEFNU3laVUJ4TnIwQ3dZRTFHZjMiLCJtYWMiOiI4MzA2MTdiOGVmNmI1MDk5Nzk4YmE5ZTZlYjY4NDU5YzMwZmVhZDUxM2RhYzFhYzY4ZWVmMmYxMTIyZThhYWQ4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:43:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Ii9rMXZzUzhvMmg2UGpXbDBVT0h3WVE9PSIsInZhbHVlIjoiaittZlB6T1dCVFFCalFpdHhmb2NMaGppbUtrWHMvdUszV0JkWWhVS2hvZWFMSEMrazhJTklzcG1CQlB5KzRRYjVwaXBra2ZFaVNZakcvZHh6REkrcTV0cGxxeTNnOEhRdnV0TnAyWWc5aURZemJ1TnZTb29JcitSb2FuUmk5VVNaeC9taXJGZ2JCYVMzYnBJT09NM283QTl0ZmFENFhnS2h5S2cvZ20rNTZxU00xN3IwSUxycVpuWGxQNXQ5a1BLVFI3UTRSZ093dzQrNTBjZHgrT1UveHc5U3lVK0p3d1M5VGxqcFNIZEFaYTJLY3ozN3JBR0tHNERSMUFmb3FvWDJiU0g4ZEo2L1dxemQ5eEhFdTdkMHV4TVRKMUR4YldzL2ZDeTBPUU44czg5RHZrOUQzRGx2OWErbWZtMVVhRGlUKzBKV3g0UG1UbVM2VUdKNU02aUhkMmdYVjJ5KzlHZ3QzbFVzRkpWL1Vydms2SVJMMDFPQ2dzeEJucnpGNTFQQmVSM1hma29iNDF0dEJ5djI2dG9aRElIMjZvdHdKSlI1aUFJaEFRaFk0VEV4M2hVY2NGbjkxWFYxWFcvd3dOQVF1ckhUTURrRmNVZXljS1JtR01JcDlkdXFLZGRRdGFIS3QyUUVMVlhlQmtkZnd3Yi9Bekh4ZG9wTkpQd2ZtL3kiLCJtYWMiOiIxMzUwYzMxYzIzNjBjMDlhOTBjNjI5NTkxZWE5M2VlMzQ0MjIxN2Y3Mzg2YzQzOTA0NGYwZDQ1YzZkOTAzMDkxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:43:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZPRlZXTzlFWkxsdGJnRTMrVG1sRUE9PSIsInZhbHVlIjoiOFdTRDlocXpWVHhFT3ZjVjR0T1loRXZhZ2dGUi96dHlheUVkLzFtb0x1TVhkNXFYMDNwRHlyVkp1MnM0MERHdmRZYXdaNGhqN3YwUGx5N1h0aUZSSmNPRityOENaMjZydm1YVG5VQ3FTcVM3UUpjQUxQM3ZRdnRVR0M5cXdhZ3RITGJQZExVdzk2ZVFZQ1BOMXB1dDJvRFNlRGVqbkFjelNFTXpZeUR2VzBnZ0F0dHZZZjYxbmVSRlVjb0dRRy91Rjh0QWVYUHJ5RWFvQjVrY2M0eDByN0MrTmRLRTZhYk9SdWVkQURoSG5vcHBqRERkU0U5blIxdFB4SmViYUROMXZrWWVTcnI3WXRQd3kzWjBOcXBhUStaTXN3dmRZQnRJRmdIRHhDcGp3aFVBNi9PL0g1dVRzQnQxWGRLb2FFbnp2aDF4RE1CUVB5aVFOTFdxeXYzNjRkUGtWa0tqTU5GZzY3VGI0ZVJRaVYyNFJxY1hUeXlHSzhjUldHZ1ZnNEdEZnJpcnpDM1FZSkduV2FiUnZsNGhNeDVhMVhoLzQ1b2pjWXhkbFFoWDJ1aldhNW9VcjdOckN3V3NBWnpTaTM0MTJVSU1NWHY2bmErOFBWUjRGVUhHZW1FaUpLbVN3eTBLSHZMaEs5blZXR2FLcEFNU3laVUJ4TnIwQ3dZRTFHZjMiLCJtYWMiOiI4MzA2MTdiOGVmNmI1MDk5Nzk4YmE5ZTZlYjY4NDU5YzMwZmVhZDUxM2RhYzFhYzY4ZWVmMmYxMTIyZThhYWQ4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:43:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Ii9rMXZzUzhvMmg2UGpXbDBVT0h3WVE9PSIsInZhbHVlIjoiaittZlB6T1dCVFFCalFpdHhmb2NMaGppbUtrWHMvdUszV0JkWWhVS2hvZWFMSEMrazhJTklzcG1CQlB5KzRRYjVwaXBra2ZFaVNZakcvZHh6REkrcTV0cGxxeTNnOEhRdnV0TnAyWWc5aURZemJ1TnZTb29JcitSb2FuUmk5VVNaeC9taXJGZ2JCYVMzYnBJT09NM283QTl0ZmFENFhnS2h5S2cvZ20rNTZxU00xN3IwSUxycVpuWGxQNXQ5a1BLVFI3UTRSZ093dzQrNTBjZHgrT1UveHc5U3lVK0p3d1M5VGxqcFNIZEFaYTJLY3ozN3JBR0tHNERSMUFmb3FvWDJiU0g4ZEo2L1dxemQ5eEhFdTdkMHV4TVRKMUR4YldzL2ZDeTBPUU44czg5RHZrOUQzRGx2OWErbWZtMVVhRGlUKzBKV3g0UG1UbVM2VUdKNU02aUhkMmdYVjJ5KzlHZ3QzbFVzRkpWL1Vydms2SVJMMDFPQ2dzeEJucnpGNTFQQmVSM1hma29iNDF0dEJ5djI2dG9aRElIMjZvdHdKSlI1aUFJaEFRaFk0VEV4M2hVY2NGbjkxWFYxWFcvd3dOQVF1ckhUTURrRmNVZXljS1JtR01JcDlkdXFLZGRRdGFIS3QyUUVMVlhlQmtkZnd3Yi9Bekh4ZG9wTkpQd2ZtL3kiLCJtYWMiOiIxMzUwYzMxYzIzNjBjMDlhOTBjNjI5NTkxZWE5M2VlMzQ0MjIxN2Y3Mzg2YzQzOTA0NGYwZDQ1YzZkOTAzMDkxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:43:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1400285861 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>8</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">tea-&#1588;&#1575;&#1607;&#1610;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>8</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1400285861\", {\"maxDepth\":0})</script>\n"}}