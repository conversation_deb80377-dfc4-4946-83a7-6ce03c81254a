{"__meta": {"id": "Xa4244971e170d2751e7958708aee2abd", "datetime": "2025-06-17 15:42:54", "utime": **********.420346, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174972.963707, "end": **********.420378, "duration": 1.4566709995269775, "duration_str": "1.46s", "measures": [{"label": "Booting", "start": 1750174972.963707, "relative_start": 0, "end": **********.258033, "relative_end": **********.258033, "duration": 1.2943260669708252, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.258062, "relative_start": 1.2943549156188965, "end": **********.420382, "relative_end": 4.0531158447265625e-06, "duration": 0.16232013702392578, "duration_str": "162ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46345896, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1696\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1696-1706</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0069099999999999995, "accumulated_duration_str": "6.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.365314, "duration": 0.00577, "duration_str": "5.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.502}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3990319, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.502, "width_percent": 16.498}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-2056104584 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2056104584\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-900242847 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-900242847\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1526673812 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1526673812\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750174938454%7C11%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxQT1BzQ3F4aTY3V01SU2ZuVzd6Y0E9PSIsInZhbHVlIjoidDZleWUyZzluYU15WXFyb2IvWmJocXFzNTlwZWtlMWZCYXprUnRaNVFNNUhhK1RtSTFBRlpKSCt0Ri90Nk9YQmdad2oxaXEyenBoRCsreUV1aUZKajFCcUVyazY0cHhqNDZHOXI0TWRqc1FjQWhRWmNPVm5YM21zTnhQVTZVdVh3czA2TzRBNitWTW9qS05KNzEzRWxoMHBKdnpRWTVOVldxQlgxdzY0eUlpSVZkeUdhRGs4TnJ1WUZNSG9jVWFpSkYzc0tCZTJ2S2xIR3VBNElrWUxMcE1aMFErbmFPeEJmZUpMK05hZjdvdkpUTXkxVGpjMHI1Q2p3MW51SUF3Q25CY1h3V0hpQm9WZnBncFR1MEhRR2NJaHAvdmdISlVic0xFNnhLLzJ2VTh6OEtmN1BKZUxWS0lLL3U4bFR3YVdSTjNXSWJ5ZHAyejBJS3Vxb3haMWlzYWlEa1piWkFXWUNJd0tHTlhTbWVUVTducmRQM0w2QTJKR1FBNVYzZkF6ckZwd3A5VWNOUlh0WEZ2WDlOeTI4V2tIVlVDOUhVeFlZM3lwdVlBY2ZZcnk1b2hlNW9URyt5SXZWdm9zZURsaTRoUmdub3U0TkxKejFCVyswRFBxZjA4MXIvdzRnSk5qSUpRaWw3clpNQWxSZE5CMzRDbDhDQ2Z0N01ISmV3RmIiLCJtYWMiOiIwMjJhNWZlZDZjMTQzMDQwYWY1OWU3ZmJlZDIyNGE1ZjdlZmNiYjg5ZTljNjc2MWE2YmQ4MGQxYWZiYzU3NTlmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlpZQ0tMd2l0ekN1RzQ1Z2ZwOFZmckE9PSIsInZhbHVlIjoiQVI4MXFBNHZ1Wkg1ZXJmOEp5cVdtRWZ1YnRaRWpNUjNGNnF3NGQxaXR3R2tjRkRrZ2lIYi9VSkw1QTNsTzFxTzhicU1lOW5DOWF4TVBhM3FkMzZpcTIwUElIbVErcHRVYjMxczFwVXhDaHRJcEQ4ZEE5MERmZWZmVWFNMnRyNkEwam9sRXFOZXpLc2lRc0REU2ppaG9ZVUNLT3pvQ3RYNWJqbUEyYjMyM1M3dUVRa0tmaERJMmt5SnEwTVFIaFpaRk91U0dNK1VpNjR5VjVMQTRCLzRTVmc4eG4wMkFtcFN3WW4zdXAzQnBTTG83MEU2VDdwRTVGRG9yN3J0bm5QZGcvMVVSR3REczExODdyeU1qNmdlNTUwcFZMT1VqOEg5NnVaNXV2UUFPeFV5T21JcjQ3VGNHSEhmYUVyV2hpUFdhUjZ3NkZTbHc3anpSZUs1emtkK2ZrK0JxZG90aTZ0RWl2VDNzVDZpbFVEcnNYam9xMzZpQmo4MzRwdm1hRUY1ZFI2T1I5ZWtyWHBsSWtPdVNwempkejJaZnhrVHhWYk5MV0p5YmdRN2JiZFVGY2szRlVUQVN3OHM3ZWJvUnNab1FjY29tMjY0ZlFybFIxcEJrWURVV0pKYTBOS0JXQ0hWamR2Tmlqc1pvcXRGWC9NUzJWL09TUjMxNzZZbW91dUgiLCJtYWMiOiIyNjUwOTA4ODk0YWQ5YTJmMzJlYTMzZGRkNzM1NmNjOWI3MWVjMDk2ZWY4MjdmMjAwNTQ0OTQzM2M0MDliYWVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-663977953 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-663977953\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-205372092 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:42:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9YeXZyWFpNcVhVUnNNRlg4MmtMeHc9PSIsInZhbHVlIjoiUUFubk9sNzdVNTJ3bW14MS9leklGVjNsa2VXQmF0bmp2MHYvem92YnF2STBxUW1kdGNOQk1EYTd2cmdvdHBSQ2hzTERSRWN4SXNmYm0yTlN3RmR2c214WDVCNmdGcUFJcmdnOUpPcEVSZndHeEEzQUt5dUJ4V3BYTmRrR3BhLzRUTTRzaEduK2NNamNhcWloU2FmdlNhWTQyei9qTHdEa2VXbDhYcTRPL0w4VFA1VFBaMUpIZUhNaW52UWZURmVxWTVZeHpqU1RoTkJpNlJaU29GSFpacmhiZUdITGpOQzc0V2xNVGRZbll2Nm5ZSkZmaTgvcEFybEYrOVVrRUh3dm5FbmdHc1FNek95ZlpOaFVOaUZ1SHJBYi9reHJuT29IdjFnSnFvZFF3bTdxbGV0ZnR5Y0lTdElnK2FDT3JNWkV2a2tVbVhQOEhXZm8vZnFpOGdZT1U0cElGU0FiNlBhdUhIdDJwU1VaV2RYRjZKTll4Y0RONUFuOWV0R0s2NWRGaW9aTW1EL1ZSOHFlb2JkZ25tWG5jbmJVanBrTitMUkRnYnNTdlM3cW00bDJFNkltTkVqOEwzbFRyRHJnVWxmMWlRMzZJa3dsYjVEQjJNWktYcHNEY2dqclVjaTBiMEpGQkNrOEl6eENiQ1BNa1krVklHT0NLUnU0MmdnRXlzM1IiLCJtYWMiOiJmMmM2NGQ4MmRjMjUyYWNjOTkwNWJmNzg2YzZmOWY5MmFkMjAzM2IwMDg1ODI0MTAxY2Y4NjhmNTkyNTM4ZmJkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Inl2QU1sejNKQkNLOVpPclRCenBiUnc9PSIsInZhbHVlIjoiekVGV3dvRDJnV1FmTjJhNlZnM1hjWWRIM2ZBeUNGQTB6REtEOXJiaEp3RGNUb0NvODZoNmphSWZXTHcxM3AwdHVmSkVKQVVsOGloNUc1VjdON21udm90eHhjZDdjU3d4YUdGOU11aUZMWW1zMU14c3VTb3lNVzFyekNJa2VnUHg0TWhzNE5HSHQrZFFTNWZkbXlwUDRPY29GaktodVhOQk16clZ5c0VNbjNtVmhJZWtwRVpyb25sRE5tcmdXUGl3dTBVa2lhUGxIRHBkbVAyMXBYelBuNFRjRTB5Z0pTVUdobUZDUlAxWFl2ZmlBYnNuVnZjUUhENGYzb09LWnJuSlhRY29ldjQzV0JJeENqS0duOXNFTlllSzY1RG9wVGVVK1kzUlE1VHVPazgrZU5Tdk5Ib0FMSTg1a3lrY05qR2s5UW5GMEJtWWw4U2RWRDJ3OVErbUFWTmxDV2k0MlFrc29BdzZ4NXAyeG9lUWY1djNYZDUvU2pRemhzU2tSVURmREZuL254bzdENEE0V2xrckk5TzgrL001RWV4NzZ5S0VMZWlaUjgxVnBnZWsrOUtJek9rL2ZBam1jcHRlZVZJcFY0clBMQXhTYUFKQVM4cUEwaThXWVU2WklqMk5EZEhpWGkvaTQ4UFVXbXZOdFA4clF4RnQ5S2NiSTJ6em5Pc0EiLCJtYWMiOiI4OTNkYjM1YjFmMTUyMzE1MmFlYWM5OTJlMjhlYjVhMGY4YjYxZjU2ZjFhNmJiOGNmYzlmOWJhZjIwOGQ2YjAxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9YeXZyWFpNcVhVUnNNRlg4MmtMeHc9PSIsInZhbHVlIjoiUUFubk9sNzdVNTJ3bW14MS9leklGVjNsa2VXQmF0bmp2MHYvem92YnF2STBxUW1kdGNOQk1EYTd2cmdvdHBSQ2hzTERSRWN4SXNmYm0yTlN3RmR2c214WDVCNmdGcUFJcmdnOUpPcEVSZndHeEEzQUt5dUJ4V3BYTmRrR3BhLzRUTTRzaEduK2NNamNhcWloU2FmdlNhWTQyei9qTHdEa2VXbDhYcTRPL0w4VFA1VFBaMUpIZUhNaW52UWZURmVxWTVZeHpqU1RoTkJpNlJaU29GSFpacmhiZUdITGpOQzc0V2xNVGRZbll2Nm5ZSkZmaTgvcEFybEYrOVVrRUh3dm5FbmdHc1FNek95ZlpOaFVOaUZ1SHJBYi9reHJuT29IdjFnSnFvZFF3bTdxbGV0ZnR5Y0lTdElnK2FDT3JNWkV2a2tVbVhQOEhXZm8vZnFpOGdZT1U0cElGU0FiNlBhdUhIdDJwU1VaV2RYRjZKTll4Y0RONUFuOWV0R0s2NWRGaW9aTW1EL1ZSOHFlb2JkZ25tWG5jbmJVanBrTitMUkRnYnNTdlM3cW00bDJFNkltTkVqOEwzbFRyRHJnVWxmMWlRMzZJa3dsYjVEQjJNWktYcHNEY2dqclVjaTBiMEpGQkNrOEl6eENiQ1BNa1krVklHT0NLUnU0MmdnRXlzM1IiLCJtYWMiOiJmMmM2NGQ4MmRjMjUyYWNjOTkwNWJmNzg2YzZmOWY5MmFkMjAzM2IwMDg1ODI0MTAxY2Y4NjhmNTkyNTM4ZmJkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Inl2QU1sejNKQkNLOVpPclRCenBiUnc9PSIsInZhbHVlIjoiekVGV3dvRDJnV1FmTjJhNlZnM1hjWWRIM2ZBeUNGQTB6REtEOXJiaEp3RGNUb0NvODZoNmphSWZXTHcxM3AwdHVmSkVKQVVsOGloNUc1VjdON21udm90eHhjZDdjU3d4YUdGOU11aUZMWW1zMU14c3VTb3lNVzFyekNJa2VnUHg0TWhzNE5HSHQrZFFTNWZkbXlwUDRPY29GaktodVhOQk16clZ5c0VNbjNtVmhJZWtwRVpyb25sRE5tcmdXUGl3dTBVa2lhUGxIRHBkbVAyMXBYelBuNFRjRTB5Z0pTVUdobUZDUlAxWFl2ZmlBYnNuVnZjUUhENGYzb09LWnJuSlhRY29ldjQzV0JJeENqS0duOXNFTlllSzY1RG9wVGVVK1kzUlE1VHVPazgrZU5Tdk5Ib0FMSTg1a3lrY05qR2s5UW5GMEJtWWw4U2RWRDJ3OVErbUFWTmxDV2k0MlFrc29BdzZ4NXAyeG9lUWY1djNYZDUvU2pRemhzU2tSVURmREZuL254bzdENEE0V2xrckk5TzgrL001RWV4NzZ5S0VMZWlaUjgxVnBnZWsrOUtJek9rL2ZBam1jcHRlZVZJcFY0clBMQXhTYUFKQVM4cUEwaThXWVU2WklqMk5EZEhpWGkvaTQ4UFVXbXZOdFA4clF4RnQ5S2NiSTJ6em5Pc0EiLCJtYWMiOiI4OTNkYjM1YjFmMTUyMzE1MmFlYWM5OTJlMjhlYjVhMGY4YjYxZjU2ZjFhNmJiOGNmYzlmOWJhZjIwOGQ2YjAxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-205372092\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1746206175 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1746206175\", {\"maxDepth\":0})</script>\n"}}