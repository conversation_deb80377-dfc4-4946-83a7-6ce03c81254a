<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckCashier2Access
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated
        if (Auth::check()) {
            // Allow access only for users with Cashier2 role
            if (Auth::user()->hasRole('Cashier2')) {
                return $next($request);
            }
            
            // Deny access if user doesn't have Cashier2 role
            return redirect()->route('dashboard')->with('error', __('Access Denied. You do not have permission to access this feature.'));
        }

        // Redirect to login if not authenticated
        return redirect()->route('login');
    }
}
