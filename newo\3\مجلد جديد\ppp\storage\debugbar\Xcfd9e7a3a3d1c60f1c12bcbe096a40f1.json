{"__meta": {"id": "Xcfd9e7a3a3d1c60f1c12bcbe096a40f1", "datetime": "2025-06-17 15:42:34", "utime": **********.918432, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174953.425732, "end": **********.918463, "duration": 1.4927310943603516, "duration_str": "1.49s", "measures": [{"label": "Booting", "start": 1750174953.425732, "relative_start": 0, "end": **********.723595, "relative_end": **********.723595, "duration": 1.2978630065917969, "duration_str": "1.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.723625, "relative_start": 1.2978930473327637, "end": **********.918466, "relative_end": 3.0994415283203125e-06, "duration": 0.1948411464691162, "duration_str": "195ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46331040, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1696\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1696-1706</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.020629999999999996, "accumulated_duration_str": "20.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.840208, "duration": 0.019399999999999997, "duration_str": "19.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.038}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.894141, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 94.038, "width_percent": 5.962}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-848846110 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-848846110\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1149945296 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1149945296\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1261698798 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1261698798\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1776260080 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750174938454%7C11%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtWeVZoUkNUbWtLY3RuNjhXaEtTVFE9PSIsInZhbHVlIjoiQ1ZIMDU3ejZSWHE0cmk4UmdobnJRbUxoSUxMZnFCVXNXMWNrZGJlbGd6QkdkdkZkN0lPcEdtc2tpRCtJOEo4N0tKZDBKaURrY1NCdkhnVnlPL3ZjcmtJVXVRYVhLMTZEckNNbmRFU3NjbmRTaGptT3oybzhLbUlPR3NWNTcrYmxvTE01M081T2l3ajR2aUtERVgvYWJna0NHSUxwMTAzYzNGK1Z0OHh3c3lkbkxWcDVqTWxqVGt6MDhXc2wybFdxcGF4L2p3aFNud2ZyRkJZeEc5NzJjWmRRbDZ2djFVQXU0V3I1SkVlUzRHOEhCc1BuNzBKcUhtcHJrNjhaQ1ppUW13K014b2VVcUxZc1l1RmYzdTV5VXNRcStDa3ozQnl1WWtoa0orNjQ0YWNNN1ErN1VPYlNWNGFJckg0T2JaK2pwbWswOE1yZjNSMlE0aDY1elBXS20rRWRpVzlQV1V0YW9IZ3BLQllVQ2lTN2k5WW55VS9wTTcrajRrN2c0clk0cXNIZThVZllUTWdNY1hoTTNXaVZCcjkzZlJJTnIrTExVRnpjaWNBTzc3SlBad3RSZTNyRUFOQkREeGFZYnZLY1pLUlFkb0Qza0R6TkZlWUxxbEg2NXpBaFFTczJPNFYzNXo2dnZsV3NjeDh2Lzcrd2VHZ1FsUFdJS1BYVE5hMHgiLCJtYWMiOiJhZjcxNDBlNjQxNjRjZDVmNTk3ODBkZmU0NjI4ZjM0MzNlZGVlYTAxYzAzOGFiMzFkOWFkYjMzMTYxN2FlZjkyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImZhUlYxWnBtNHlQa3dyWEwrQzBDSUE9PSIsInZhbHVlIjoiYmFMUCtqTVNLblRRZVhmYUMyeTlCckpUaVhoYVVINUJhNVQzSUNWeWtrclFRNGVKOXVZS3JXc0xZeEl2dm84bjlnOUQ5VlJuOXp5OXVEbVM1SWFqV2hqM3NZUXdhUkxHSjljMWF4UDY4WUxYVDhGK2E3cmFURHhnWmd5RXhVSU4zWGRmdWJ2TlhyNG1iR3ludjVjQnI3cjJ5UXZoS3E0VDNNSkY0VVNNRGlNSk0rMGlSUThZS3B1dmR2WWhZZEpiL2x0Zklvc3hzandGa3gzRkVpbHZGZFZJZUZVL3IyRmxJWHdQY2tqdGErSHJjalU4SGxySUFqRG5zSmFQeW96Y0pTWG84d0g5Yi9KaXUvc1RadmZvS295S21zVWh6VS9zMG1GS1M1LzNacG5tSW9JTlhGMUxDckc1VGxDNjgvRXlCOTZIaW8wcERHMVFmMGxpMVdldEdPVjV2aXhTd1U3ajEyeUJLRC91cjBCeW1QL3V6RmsyVXk1bVNsMDJIdUhMYTU0eXA1R2JJUm9KMm5EVERiOG1vQWt6M3Vuck1Vcys4aWk5cXE0SENseTFmM0k2T1NrQU4weFJEaFc3cXVHTXdlR3NweFBSbVBtRGpsTzJtTVgwc2FSbGI1cHFJNFgra3JUdmFUdlI3VVFGNzZPbkVPVGIxelZSWmtucTdxWlIiLCJtYWMiOiI4YzQyZmY0MDhkYzc2MGQ4MTUwMDI4YjhmYjFlZjYxOWUxNzliYzI1MjA5ODUxOTI1Yzk4ZjI3Mzc1YjdiMTAzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1776260080\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1682223436 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1682223436\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1743476654 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:42:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNFV0c2VkFKcnByd2JSVng4b1dsY3c9PSIsInZhbHVlIjoicDlqWkZqS3h4MCtWMnBObjlKbFpEaWJadmpCcGJOZEdKQUNWOW1TS1BER0FNeDQyb0pEK1JpbUJHY0lDRE5EWnBwTXpMcDUxMFJHL0NEM3hHc2psYTZCdUtTRy93RmhWMUNaOUZ5NDRFNWJNWXY4MHhGYmZYcThSQWR2NjM3b3NIT0ZmOHZJTjMwM2FlUW5PUHhaMzNpazJaeC9ZRFRpKzNXZFNqNExzQk91NmhJZTB0VmE5TE1IbnYwZko2Z0hPbi9hWDNqbFRRVlZwYXhBdWhnYkFYOTdDeDJGQ2lNMTdvS1o4YjV6M3NrM09kV0J4Sk43aHM2VFc3T3Fqanc3bnFFVDU2SkVWTGFyZE9nai9talRUY1JXK3JvSmc1MEFVVEFWUjkrSytkbzdUMWVIckdheTdnRldhTVRBWGRxd0VUbi9QTng0TWVKUmRnTzBMaVJpUUtJY01LeDlSMWtjdU9YWjQyWUdxSnRJb2RuK25ROGxmN1FMOU5TQVk0YitxMXVCcnhKQVl0NFkzRXA4MER3WUhnYkhzN1hRVEx5cGdZNzdyM0NGblZpdkFOM2ZNUUJwRkh1ZkhxWnIxUGRyVFp0cGJaQmZFb2FRUFBaOVJ0R3pobUsxaWZDQlA0UDhIT0VyK3hZM0tqbmpsUFMrMnYzdExEdG9GMyt0Q3Rvc2UiLCJtYWMiOiI0YTEyNjlkMWY5NzNkOGZjY2RlM2E1MTdiY2FmY2I2NzhmZjZjN2EwZTFhYzU4OTNhNGNkYjlhODYzNTFjYzg2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImVFZHZUMzJ3UkdrQitIQ0laa0pJYnc9PSIsInZhbHVlIjoiTjd4dUFiTitjZFVVWlNaL1FZQVFiYk43S1pacm81alZBWFJSRkg4TnErcjlCd0JmRi9mdXR6NE1ka0pUNysvVExVQWd6TXJiSGFjUXJCdGdwaC9lQXRmTXQzQWlDQlkxRHVkN3hITzZDeTk1SVExZ3VsK2RSMzF2Y3NvbWgrb1lFbjk4ODFnbTdleWsvbm8yWUFPNS92R0huTkNUZmZMQ2FUVXlaUWd5c2hoTmZDUXBobjFFMFp2ZGFPcVNsVFV1L3BWVVhleHhwWmNlZ1hmSEc5dTdvQ2pNQTlqd2MvZkwyV212TFBGcW1idmJjbGZZRGdvRS95VGwxTXdndEJZWXlIMm05SHlMSXhyQ0djS1RLcGlZNUQxUU9ucVFxMVM2Vy9OcERTSGsxV1FORERRWnBXSUI4MmQ3eit0cmFJeGx0NUdWRFF3aFVZbVlPM3ZpZHAvWkN4RTUwbjVSNCt0R0pVL21NdWQrN2pZMVEwQXBBMVBXZGZzT0JaSjQwWlA3blUvRDZ2MVZ5ZmYwMTI2WjdCTkMrb0ZaQ21jdG9FQWNzUzdPQnl4QzRiS3lVeWl4cjhpL1JCZmVCUkdSVmNvK3M1bEVmbTlKUGt4eEkzOVpLbmdybTRFc1RFbkZ3Vm5uVWVYZTlqYlk2dHArQ3FTVE45TmlaMS96TVEzc2dubWciLCJtYWMiOiIxYTQ4OWYyMDNlNjQ2NWYzZDY2MDg2M2VjNzI4ZTVhY2FmMDgwZTgwOGFkZWFhNDU0YTkxZWIyZGEwNzBiMDQwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNFV0c2VkFKcnByd2JSVng4b1dsY3c9PSIsInZhbHVlIjoicDlqWkZqS3h4MCtWMnBObjlKbFpEaWJadmpCcGJOZEdKQUNWOW1TS1BER0FNeDQyb0pEK1JpbUJHY0lDRE5EWnBwTXpMcDUxMFJHL0NEM3hHc2psYTZCdUtTRy93RmhWMUNaOUZ5NDRFNWJNWXY4MHhGYmZYcThSQWR2NjM3b3NIT0ZmOHZJTjMwM2FlUW5PUHhaMzNpazJaeC9ZRFRpKzNXZFNqNExzQk91NmhJZTB0VmE5TE1IbnYwZko2Z0hPbi9hWDNqbFRRVlZwYXhBdWhnYkFYOTdDeDJGQ2lNMTdvS1o4YjV6M3NrM09kV0J4Sk43aHM2VFc3T3Fqanc3bnFFVDU2SkVWTGFyZE9nai9talRUY1JXK3JvSmc1MEFVVEFWUjkrSytkbzdUMWVIckdheTdnRldhTVRBWGRxd0VUbi9QTng0TWVKUmRnTzBMaVJpUUtJY01LeDlSMWtjdU9YWjQyWUdxSnRJb2RuK25ROGxmN1FMOU5TQVk0YitxMXVCcnhKQVl0NFkzRXA4MER3WUhnYkhzN1hRVEx5cGdZNzdyM0NGblZpdkFOM2ZNUUJwRkh1ZkhxWnIxUGRyVFp0cGJaQmZFb2FRUFBaOVJ0R3pobUsxaWZDQlA0UDhIT0VyK3hZM0tqbmpsUFMrMnYzdExEdG9GMyt0Q3Rvc2UiLCJtYWMiOiI0YTEyNjlkMWY5NzNkOGZjY2RlM2E1MTdiY2FmY2I2NzhmZjZjN2EwZTFhYzU4OTNhNGNkYjlhODYzNTFjYzg2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImVFZHZUMzJ3UkdrQitIQ0laa0pJYnc9PSIsInZhbHVlIjoiTjd4dUFiTitjZFVVWlNaL1FZQVFiYk43S1pacm81alZBWFJSRkg4TnErcjlCd0JmRi9mdXR6NE1ka0pUNysvVExVQWd6TXJiSGFjUXJCdGdwaC9lQXRmTXQzQWlDQlkxRHVkN3hITzZDeTk1SVExZ3VsK2RSMzF2Y3NvbWgrb1lFbjk4ODFnbTdleWsvbm8yWUFPNS92R0huTkNUZmZMQ2FUVXlaUWd5c2hoTmZDUXBobjFFMFp2ZGFPcVNsVFV1L3BWVVhleHhwWmNlZ1hmSEc5dTdvQ2pNQTlqd2MvZkwyV212TFBGcW1idmJjbGZZRGdvRS95VGwxTXdndEJZWXlIMm05SHlMSXhyQ0djS1RLcGlZNUQxUU9ucVFxMVM2Vy9OcERTSGsxV1FORERRWnBXSUI4MmQ3eit0cmFJeGx0NUdWRFF3aFVZbVlPM3ZpZHAvWkN4RTUwbjVSNCt0R0pVL21NdWQrN2pZMVEwQXBBMVBXZGZzT0JaSjQwWlA3blUvRDZ2MVZ5ZmYwMTI2WjdCTkMrb0ZaQ21jdG9FQWNzUzdPQnl4QzRiS3lVeWl4cjhpL1JCZmVCUkdSVmNvK3M1bEVmbTlKUGt4eEkzOVpLbmdybTRFc1RFbkZ3Vm5uVWVYZTlqYlk2dHArQ3FTVE45TmlaMS96TVEzc2dubWciLCJtYWMiOiIxYTQ4OWYyMDNlNjQ2NWYzZDY2MDg2M2VjNzI4ZTVhY2FmMDgwZTgwOGFkZWFhNDU0YTkxZWIyZGEwNzBiMDQwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1743476654\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1023361879 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1023361879\", {\"maxDepth\":0})</script>\n"}}