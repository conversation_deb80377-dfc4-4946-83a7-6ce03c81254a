{"__meta": {"id": "X6ec6cdf4fc8d3248e2a72ac3717044d1", "datetime": "2025-06-17 15:42:36", "utime": **********.173161, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174954.56194, "end": **********.173197, "duration": 1.6112570762634277, "duration_str": "1.61s", "measures": [{"label": "Booting", "start": 1750174954.56194, "relative_start": 0, "end": 1750174955.987724, "relative_end": 1750174955.987724, "duration": 1.4257841110229492, "duration_str": "1.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750174955.987746, "relative_start": 1.4258060455322266, "end": **********.1732, "relative_end": 2.86102294921875e-06, "duration": 0.1854538917541504, "duration_str": "185ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46219984, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01008, "accumulated_duration_str": "10.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.099649, "duration": 0.00717, "duration_str": "7.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 71.131}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.136961, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 71.131, "width_percent": 12.897}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.149334, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 84.028, "width_percent": 15.972}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-195264490 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-195264490\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1217266846 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1217266846\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1177838588 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1177838588\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-338442055 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750174938454%7C11%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InAvMWFQUUVhUFpoR0dMMlE0SFgzUXc9PSIsInZhbHVlIjoiRGVFNExZMjg3VXdiMmkxQWcvcU9mc0VHZ1QyMEFYSCt0Q09abFlIV3loN0h5SnMzTlpVQVJGNjYwbGg0YTROUjFpdkRtSG9kdFlpVlhQeXFxVlZWVi9zUG12T2pjT2paNjNTNFlKWktuU3AxYmRZaHBOUTIyM0kvSmw2OHpRSTc2a1BkZnFzUXF5U0c2cUoyVkNOeEdhTUZCb2diZ05RdDJaRUFxcXNHUXowVzZlUm1wTHRNaWFNc2RxVEEvUFJJZzJGVFhVYXNVZ3lNQjRlZkRNK0lOeWs4eStHRzNtVW5XSUpXMkcyaE52QldPWW9qenFBcnFyMGhhUkR4cXdBMS9xZkJSRDYvWWNrL3hLVCtFSVRhV1AvTU5KRnRNcitjR3NSRzVYL3VhbHVtdjQxd0FrYWpZb1I3OXRhNmNnQlB6NUEzMmV2blZna0N4NXI0T0xNT01lNmxGU3lUcWhoMmVzaDZjVFYxM213WElldDlINDIvbWJMcWJDTmRNMTN5eXFiZWV3K1ZsQTdGYU9ZbTZGZSs1a2ZpS21OWXVWMEZwRG1YemRWY1g1UEovcS9oQ240R2ZvSHVzV3VKMDY2eVFEQTlJMlN2am9tejVzOHVlWjIwSmttcVFhajFZQ3lzNzB5bnc2V29TckpLUjFDcDVKWVJtMGttL0NBNWltejkiLCJtYWMiOiIwYmZjODE4MDc2YmE0NGE5NDJhM2ExNGRkYjBkODM2MWE5OGFhMjAwMWJiNTIyOGQ5Mzk4MDAyYjNhZmEyZGY3IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6Iml4NHhkUm1vSHJlK2w1MUJEUGhENXc9PSIsInZhbHVlIjoiQWxhUVA4QnAvdWJzZG1zK1ZXTUo5WndIbC9URHd3M1kzTlE5eUhtKzhxVDcvZlNJd0VCODMwaGZHczBiaFFjeHBIaTRBNnlvQ1NGV3FCK1FoWjRuTmx6ZFNBd3lTUHBFVkY0bGZIbytzSzBnZ0ZJZndIZEV1Z0RlZ3VQQzlrK3BkS0FuTUtkNm9Ec2F5b3pXeGY4SUpWZ0tXTzROWHFudlRpY0dXczJPek5jMWhTVkxXSjFWTCtJTXBXSjlNS0RkcnpEdjBqNTloMTUyU2hYVUtiVy9QODdPMDVyNHJ0SXhYZGhZc2xRMTI3aERRdko0cXFmTjYxZU03YmY2THI5K29IQk5CZzJ1WWN3ckJFbEEyR1g0TVN0Vjg0SStrR3VKeTg3ZTVhb2tDeE5GWUxacTQ2UUZOVlNlS2Z6a2NaeUR5SGc5VXg2VUVQUEgxZmQxVkRoSjFrR0Jzc3JuWXE2bkc2VUdrM3J0V05hR1BKWWY3NWVnM0ZQejhKckdBZFQ4L2d0UGVRVVRvbHI3NU9EcDF5bWxFTXVwOWpGazlwSGhtOVdSQTlMZW1GVWdQZllvM0dUYU9FNjl2dU1tZjRwK3ZxYmoybmZwbllGWW9lMStndjBIcFAvK0xFd24rdDJXaGFoRWNMQzdRUkdOc09qRUc2NGJQQS9HUlBkZVpLek0iLCJtYWMiOiI5NzUyMWE0NDcxNzIyNTE0NjEzZWFjNDkyNDliYWFjZjVkNDkzNjlkNjRhNDIyNGNjZDQyZWRlMzliODZhMDc1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-338442055\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1068397404 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1068397404\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1829846295 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:42:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkxqUXRxaHI2WS9ZMTFKb09lZHZXa3c9PSIsInZhbHVlIjoiSHEyYlI4cHZ4UWRIaVJtTEI2WnF6WmdmL3h5SzFxYnpjaTdySm8rbVB0UC83VzJ3L2diOFVIV01XYnRQaEUzS2J0RGNMaTRiNTFlL2ptdkthdUZHWERxbGd1aGRFelkweGFiVnh3b1RVeVhSdHNQSkFEOTBBbmU5bXdFWGE0Zk1MN1FBcCsxZW5ZUnFUV2pTK0J4b3hHdzdCYlB4SGdBUUt6NVhEdzJkM0cwZHN2bkx5UEc3MEJ6aFVkT3FTNTVaUkprUGx2dXdWSC81bU5SVi9vMTFRd041S1Z0bTJyMU5JOXBIZCtwZkN2NTZaVENzTXJIR2NGZlNrWjNWWHlDRjNxdGVJa3Q5Q1c5SEFvZ2YrdDYxaExiNy9pdUtma0M0VnhKODdOM3JVdFlFbEhBc3lPWkN6SW1xRmk3M3JBenFVTG03NHJ0QUQ2UTZhV2l1V2ZTTENTZGFML1hhcitiOEdtQXpPOTZDWEI0ckZ1UW15V1lUUmVsMWNpT21QdjBKV2pHQUpFMUFIRk5HVU1pdXlpOEp4T1dmRmdzSWpRTzV2dk9zcXRGSjlSRHpTWFBPMDJkMjJhM0xFOHVzOTBuOVFvb2p4WDErNVRsU2w3Q0xIdEpnTFlXMmVBazJhMmlVdTZPT2ozaVhCUWQ5cE44Q1JndnpBV2NxRHFoeCsxSmIiLCJtYWMiOiI3N2JmYWQwMTRiZDUwZTNmMDRkMTcwOTE1NmY1ZjViOWE0MzljYjI1ZTE1YjRlMmNjNDA2MjUyMDEwOGVhM2NmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InNEeXJJVHJiWnlYSHNZQVRqQi9oeVE9PSIsInZhbHVlIjoiYUVSUU53QkdKc3MvdUhUeVNQVk02Q3JNcWtDRWJZRGtJVVUxMG9ocUtrSGh5Q0xxYzlUY0llSXFRamFDSVlYK2hGTWo3M2ZrNEhyZzlIM2N0c1dlUmdIYjhVV0xRUkppaXZaTFNueHBjVGptT2Izbk42UmZHenlYRnhiZkNWcitST0hOVUEveXFXcm9zMEdlYmZpV0F2SjB3SUk5Uko3N3BySUJIb0x4YjdSYlUwUlpwTEJham5XWlRrcWg0SHcvOEhTK0VYV0hjb3R3Yk1LbGpqTDBnRGpHYmdERHlJR0o2dVB5amdzY2xudGNZMmZHOUNLTXZxOVlGUWNiY0o1LzNPb3BqeGRWei9iV3VTaHhiQ3ZyL2NwQndMWXVmZjNONDFLSERmd2Nwc1h6L1Q5Q0Yra01xbk5WdFFKd3doWVQxNEJHTjhNejBBYzd3bjFLcUdsdytEWjNrUDZwU3NEeXk5d3RmcDhaVFBLNCtYMUp0YTk1RnlSVEt1dkhERTBBSVZFU1N4cXNwVzM5UW1BbFNHMUtuSDU3RGE3dmQ0T0tYbmkyMzlxVFFBWDNGUnFYVTl1V1p3bEQ5dUdZYkFKRjA5b1VJK0FDOHFSZmZTUmgwWXpkUXRvazNJazZjeXprcFBRaHJyUk90NFRhT0xWcmVqcHJTOUFnTEVqazY4WDMiLCJtYWMiOiI3ZTg5ZDAxYWU3YTY1MWYyODAzOGMyOWM2MmM1YjI4MDI5NGEzZDdlZjdiY2ZjZjMwYTk3ZGRlYjk3ZWNmZjhjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkxqUXRxaHI2WS9ZMTFKb09lZHZXa3c9PSIsInZhbHVlIjoiSHEyYlI4cHZ4UWRIaVJtTEI2WnF6WmdmL3h5SzFxYnpjaTdySm8rbVB0UC83VzJ3L2diOFVIV01XYnRQaEUzS2J0RGNMaTRiNTFlL2ptdkthdUZHWERxbGd1aGRFelkweGFiVnh3b1RVeVhSdHNQSkFEOTBBbmU5bXdFWGE0Zk1MN1FBcCsxZW5ZUnFUV2pTK0J4b3hHdzdCYlB4SGdBUUt6NVhEdzJkM0cwZHN2bkx5UEc3MEJ6aFVkT3FTNTVaUkprUGx2dXdWSC81bU5SVi9vMTFRd041S1Z0bTJyMU5JOXBIZCtwZkN2NTZaVENzTXJIR2NGZlNrWjNWWHlDRjNxdGVJa3Q5Q1c5SEFvZ2YrdDYxaExiNy9pdUtma0M0VnhKODdOM3JVdFlFbEhBc3lPWkN6SW1xRmk3M3JBenFVTG03NHJ0QUQ2UTZhV2l1V2ZTTENTZGFML1hhcitiOEdtQXpPOTZDWEI0ckZ1UW15V1lUUmVsMWNpT21QdjBKV2pHQUpFMUFIRk5HVU1pdXlpOEp4T1dmRmdzSWpRTzV2dk9zcXRGSjlSRHpTWFBPMDJkMjJhM0xFOHVzOTBuOVFvb2p4WDErNVRsU2w3Q0xIdEpnTFlXMmVBazJhMmlVdTZPT2ozaVhCUWQ5cE44Q1JndnpBV2NxRHFoeCsxSmIiLCJtYWMiOiI3N2JmYWQwMTRiZDUwZTNmMDRkMTcwOTE1NmY1ZjViOWE0MzljYjI1ZTE1YjRlMmNjNDA2MjUyMDEwOGVhM2NmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InNEeXJJVHJiWnlYSHNZQVRqQi9oeVE9PSIsInZhbHVlIjoiYUVSUU53QkdKc3MvdUhUeVNQVk02Q3JNcWtDRWJZRGtJVVUxMG9ocUtrSGh5Q0xxYzlUY0llSXFRamFDSVlYK2hGTWo3M2ZrNEhyZzlIM2N0c1dlUmdIYjhVV0xRUkppaXZaTFNueHBjVGptT2Izbk42UmZHenlYRnhiZkNWcitST0hOVUEveXFXcm9zMEdlYmZpV0F2SjB3SUk5Uko3N3BySUJIb0x4YjdSYlUwUlpwTEJham5XWlRrcWg0SHcvOEhTK0VYV0hjb3R3Yk1LbGpqTDBnRGpHYmdERHlJR0o2dVB5amdzY2xudGNZMmZHOUNLTXZxOVlGUWNiY0o1LzNPb3BqeGRWei9iV3VTaHhiQ3ZyL2NwQndMWXVmZjNONDFLSERmd2Nwc1h6L1Q5Q0Yra01xbk5WdFFKd3doWVQxNEJHTjhNejBBYzd3bjFLcUdsdytEWjNrUDZwU3NEeXk5d3RmcDhaVFBLNCtYMUp0YTk1RnlSVEt1dkhERTBBSVZFU1N4cXNwVzM5UW1BbFNHMUtuSDU3RGE3dmQ0T0tYbmkyMzlxVFFBWDNGUnFYVTl1V1p3bEQ5dUdZYkFKRjA5b1VJK0FDOHFSZmZTUmgwWXpkUXRvazNJazZjeXprcFBRaHJyUk90NFRhT0xWcmVqcHJTOUFnTEVqazY4WDMiLCJtYWMiOiI3ZTg5ZDAxYWU3YTY1MWYyODAzOGMyOWM2MmM1YjI4MDI5NGEzZDdlZjdiY2ZjZjMwYTk3ZGRlYjk3ZWNmZjhjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1829846295\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1245121384 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1245121384\", {\"maxDepth\":0})</script>\n"}}