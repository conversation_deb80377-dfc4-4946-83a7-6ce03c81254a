{"__meta": {"id": "X2bc4830c62244bf723a88306354dc146", "datetime": "2025-06-17 15:42:55", "utime": **********.367495, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174973.89511, "end": **********.367527, "duration": 1.4724171161651611, "duration_str": "1.47s", "measures": [{"label": "Booting", "start": 1750174973.89511, "relative_start": 0, "end": **********.183392, "relative_end": **********.183392, "duration": 1.2882821559906006, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.183418, "relative_start": 1.2883081436157227, "end": **********.367531, "relative_end": 4.0531158447265625e-06, "duration": 0.1841130256652832, "duration_str": "184ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46234264, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02312, "accumulated_duration_str": "23.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2850668, "duration": 0.02041, "duration_str": "20.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.279}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.332279, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.279, "width_percent": 6.315}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.343251, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 94.593, "width_percent": 5.407}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-441455524 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-441455524\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2045068768 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045068768\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1964916198 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1964916198\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1345209528 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750174938454%7C11%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imd2ZkhwNlZVWGhZOVNONnIzUURyWlE9PSIsInZhbHVlIjoiVTRScWNXTnlyL2xOYlZXSWE5cnRFSEt1eE9oSVZuYXJGaSthZTZOVDRaUDA0ZktyVE1qdzVmaUtGcmk4ZEM1Q01ua3l1NVd2R1lWZThqWVBxaUtGY2VYYXBFc3NvZjlicm0vZlgvSzBncUErR3c0TDlwZHZ5UHpOUUUzdS9KVWJFTW0yQnNwamltL2FSalJHTHBYV1RMVC8veG9yRU11MEFuVmFoa1BhUXh1cnVKMVptYkNnT204WnlXUUlLOHVZdmZrZjdzYTRyUkZUay8wSUo5QnlIM1NIdWZYRXNGRnpuNUZPcFIwOHFIOTQ1Tkl2Sk0va3cwRlpkYzd1YUpOMHNISFE1ZUloOFNYaXdKZ2RaRUhYV2t2YVE5OFNxYmhZUW45NDhDOGtjK2daM0d4dDFMNTc2SGk3a0t1VVRDQ0ZBSVNlSFBveVhpR1p1c2ZqeFhuSHdnUFBzTUl5ais5dVp6THoyc1BtQ3lObUVZNGpiUEdZRDRJOVptUXBKcEUyQUhNS2MrWHJtNXdhR0laQmpSeXNZVmRLVzdBeTBSTDV6SU9xangzdE1PUEZYUzVzV2Jwckh6eTBueVNSTXROdGl5blhGTkhqNlFqWTNkTGpsQzhQMTR2U29GaVN3bWFvbDVWTitQd1JaL3FuSXg1NkhlZkUvZnFkc0FYaWVnZFAiLCJtYWMiOiIxMGMzODVjYjY1ZDBjOTlhMjc4ZmE5YzllNjA4Mzc4YzQyM2M4YjdkMjk2MzJlYmEyNDBmNzVkZDIyODc0ZjE0IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlFWd3JFbVl4cjY0VWN4Zm9kbXUxN0E9PSIsInZhbHVlIjoienZLMEk5TmVCUlI3RjdiQnBoL1dJSU0xMHpoNWsvY0tEdk9jZkIrb1ROY3B6UTdWVlNudk1uZmd4WG1OK29vb0dlMVd6YlgxK0dpa25WTXdudFZjRGg3NkJmcXVYTEFXbkdFb3JTajR5Y1FOdHBIVzhCVjQzWmNsaVdKbkVZT05JRjE3eGQvZ25GTHVCWWZieW1xendTZERMNlEwcExsVk92WmQ5cEZuU3dwNjRTZ3o2aEFhNEhTKzhsbzR4U0I2dFZYZ2Vrb1c1UDhMa1lkU2Vqd0JBNGU0Tk5PRzk2K1BneVBMcklCV3Q5SzlNK3lCK0FJZXFpcnRtMlNKRC8zQk1IYStKUnRvdC9US2NTK0N4bUdhTlQ5a0hjRHlhUCtheUdDd3FiS0I3U1dYWmdXT1NFa3hHRXU4eU44eHUxb0xwZUVIbTB1QWUxbU1jL1NQcE01aVBleENuSUhMVTlhUllPdWFFalUvdFRmUUhmTjVWdEI3TDhIYzNvZS9ocjhxK2VheTlMbkd3MGJDbVFFRzlFMEpGOE0yZVhHODlBRERTMC91UFVxQUNxSUZ0QXFVNklmSFhkOU92aEhwc0VjUGpiVlE5ZytGK1pYalJJeEdhYzFMQi90VmlOSkVwRjdKZVBnbVVDbVBpL3dRUmZaZkRqSUFtVWM5Y2NMQ2hEZksiLCJtYWMiOiI3MzM1MDQ0OThiNjk1NTU5NzU0NGRkNDI0NzQ2NmY0NGUxOWI1M2EyODE4NmVmNTEyNGNkZGE4Njc3YWY2NWU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1345209528\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-602359759 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-602359759\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:42:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFJUldUeWhrdTJrYTlsVDhtcm1aN3c9PSIsInZhbHVlIjoiTDdIYm1sUGJjWXVKL3orNXJzREhCT2d6R0dHSzZ6R0JTcEFISTgrZWR0QnVLWFk5Q0lVejBkR2FrV0xVVVRSL2NRRXhWRkcxSDl0Q0RWam5yYm8wRUlTWkFKL1A4ZzA2MnQ3bzlQOWxFRVFRR0RzdktyV3BSczY5U29TRXJQWEZpWDh6cGZDQ2p6LzQrUHlKMDNKWmNaTFRMbXJGR3h2Y3JkdzZSWXIwLy9wbDMySUxKODlpVzdueXpNemk5MUpKQ3NiVmFZVTN3akErNTFoRGVhU3B2anBNZnZjdkxOWkh2U29MUmNkWjhpd0ZXakZsUkdPbnVqNUQzTWg1c3Nadks4bG0waVhzMDE1dklMWmpwdXllbmwzU3gzcGhLdlVvcFlZMVFPWVl3ZVdOZDBGYnkxT2ZSY3FXMXM3RFJGZHQ0dUx6TUtJY0hIbmJDV0tZOWIrZzJqeFZWUitMOExxenUvc1lCYkxlOHR1YnZTVklZRDVVYXRnUjFWeHROdWhDLy90MmplbTg1L1JvVWpYY2FDMnR0cTZZUWpIM1JBZUlJV0VZQytncjBoWjlqR2czU1dROW9iaXMvOTNYVVJGRENhVHdaNHBEL3p0KzBmdEpGTVh3YysxVUd6bWZuVkVKZHArdTNHYURFZ0pMVXRmRWRPZnhqanNiVWxMNU9ZSU8iLCJtYWMiOiI1MjA0OTRhMGYyNjM2NDNmYTcyN2E4MjViNDc0YTMwNWEwZTUzMDRiOTJkMDY0MzlmMmVkNjE3ZDU0NzUzN2E5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6InViRCsrdlRMK1JLempoUDFEZUZhOGc9PSIsInZhbHVlIjoiT2tlQkRTV3dhbmJjWEZ2ZURJdkZBR2w3aG5tZ0U0TGdKNUsraWdqVXoyZmI4eHpEY29ZRzErZG9VcmMyZ2dQTmtaazNjd25sdmRsUGs4ekRPWnpYUWNYMlRvd0gyTDFTTmV5NG9DTUZEWUo3d24xbnd5QU5tcTZRdnE5cTFsNEhqSGh0RXNaejBNd1hyZkgrbENMMURuRG5VYVdLNkNMMldSaFRDSVBCK2hLaEZOUTF0OThoOTM1NmsxZFFlWEJYYnp0S2MyTHVKMkYxdHowTXVtL1NCUlZaWXc3TFdlejIxSERoSTB6Q2swblkrelJVRGhPdlJVRHVVMEd1enhqbTJ0R1RBOXZqek1VcUUrYVh1QW5tVWIwZXQ0dnFmODEyWFNqdWxZU1VvTCtlelR6eGlsQWdQS2Qzb0FJRTZlMkNOemw0eEZrZHdKOVlTWk41UmMxTWVHaUVhRHBVM3o5VUJNVC9FeEhoKzBralB5VWJmZFZiOHUvUVVET09STHhOaHhGS2Y0WlEyWVlrcUJLR0lwR3dRQmNjQnVkRTRXN2JqeEJpTFNtczJEbDdUeW9IdHNheEZlWXh4ekxpeis4b2g5YUg5K2pCVXRmKy90elpaOHZ0WVFpNEZMcUMweEMrRWJ1Vmc5d3YxRTBEblNuTlBacFdGWGF3RVdFeVFHbVgiLCJtYWMiOiJkNTQwYmMzNjg1ZTU5YzQ5ODExMDg4NDliMDVjMjM2MjAxZjEyYjAyZWU0ZTBjMDAwNTc4YjkxYWQxZWNmYWY2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFJUldUeWhrdTJrYTlsVDhtcm1aN3c9PSIsInZhbHVlIjoiTDdIYm1sUGJjWXVKL3orNXJzREhCT2d6R0dHSzZ6R0JTcEFISTgrZWR0QnVLWFk5Q0lVejBkR2FrV0xVVVRSL2NRRXhWRkcxSDl0Q0RWam5yYm8wRUlTWkFKL1A4ZzA2MnQ3bzlQOWxFRVFRR0RzdktyV3BSczY5U29TRXJQWEZpWDh6cGZDQ2p6LzQrUHlKMDNKWmNaTFRMbXJGR3h2Y3JkdzZSWXIwLy9wbDMySUxKODlpVzdueXpNemk5MUpKQ3NiVmFZVTN3akErNTFoRGVhU3B2anBNZnZjdkxOWkh2U29MUmNkWjhpd0ZXakZsUkdPbnVqNUQzTWg1c3Nadks4bG0waVhzMDE1dklMWmpwdXllbmwzU3gzcGhLdlVvcFlZMVFPWVl3ZVdOZDBGYnkxT2ZSY3FXMXM3RFJGZHQ0dUx6TUtJY0hIbmJDV0tZOWIrZzJqeFZWUitMOExxenUvc1lCYkxlOHR1YnZTVklZRDVVYXRnUjFWeHROdWhDLy90MmplbTg1L1JvVWpYY2FDMnR0cTZZUWpIM1JBZUlJV0VZQytncjBoWjlqR2czU1dROW9iaXMvOTNYVVJGRENhVHdaNHBEL3p0KzBmdEpGTVh3YysxVUd6bWZuVkVKZHArdTNHYURFZ0pMVXRmRWRPZnhqanNiVWxMNU9ZSU8iLCJtYWMiOiI1MjA0OTRhMGYyNjM2NDNmYTcyN2E4MjViNDc0YTMwNWEwZTUzMDRiOTJkMDY0MzlmMmVkNjE3ZDU0NzUzN2E5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6InViRCsrdlRMK1JLempoUDFEZUZhOGc9PSIsInZhbHVlIjoiT2tlQkRTV3dhbmJjWEZ2ZURJdkZBR2w3aG5tZ0U0TGdKNUsraWdqVXoyZmI4eHpEY29ZRzErZG9VcmMyZ2dQTmtaazNjd25sdmRsUGs4ekRPWnpYUWNYMlRvd0gyTDFTTmV5NG9DTUZEWUo3d24xbnd5QU5tcTZRdnE5cTFsNEhqSGh0RXNaejBNd1hyZkgrbENMMURuRG5VYVdLNkNMMldSaFRDSVBCK2hLaEZOUTF0OThoOTM1NmsxZFFlWEJYYnp0S2MyTHVKMkYxdHowTXVtL1NCUlZaWXc3TFdlejIxSERoSTB6Q2swblkrelJVRGhPdlJVRHVVMEd1enhqbTJ0R1RBOXZqek1VcUUrYVh1QW5tVWIwZXQ0dnFmODEyWFNqdWxZU1VvTCtlelR6eGlsQWdQS2Qzb0FJRTZlMkNOemw0eEZrZHdKOVlTWk41UmMxTWVHaUVhRHBVM3o5VUJNVC9FeEhoKzBralB5VWJmZFZiOHUvUVVET09STHhOaHhGS2Y0WlEyWVlrcUJLR0lwR3dRQmNjQnVkRTRXN2JqeEJpTFNtczJEbDdUeW9IdHNheEZlWXh4ekxpeis4b2g5YUg5K2pCVXRmKy90elpaOHZ0WVFpNEZMcUMweEMrRWJ1Vmc5d3YxRTBEblNuTlBacFdGWGF3RVdFeVFHbVgiLCJtYWMiOiJkNTQwYmMzNjg1ZTU5YzQ5ODExMDg4NDliMDVjMjM2MjAxZjEyYjAyZWU0ZTBjMDAwNTc4YjkxYWQxZWNmYWY2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}