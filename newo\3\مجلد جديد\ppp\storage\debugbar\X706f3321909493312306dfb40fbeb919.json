{"__meta": {"id": "X706f3321909493312306dfb40fbeb919", "datetime": "2025-06-17 15:42:27", "utime": **********.454926, "method": "GET", "uri": "/customer/check/warehouse?customer_id=6&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174946.157428, "end": **********.454959, "duration": 1.2975308895111084, "duration_str": "1.3s", "measures": [{"label": "Booting", "start": 1750174946.157428, "relative_start": 0, "end": **********.271445, "relative_end": **********.271445, "duration": 1.1140170097351074, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.27147, "relative_start": 1.114042043685913, "end": **********.454962, "relative_end": 3.0994415283203125e-06, "duration": 0.18349194526672363, "duration_str": "183ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46234432, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01619, "accumulated_duration_str": "16.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3818219, "duration": 0.01303, "duration_str": "13.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 80.482}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4205601, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 80.482, "width_percent": 9.45}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.431772, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "ty", "start_percent": 89.932, "width_percent": 10.068}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-186062049 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-186062049\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1658580117 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1658580117\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1721130583 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1721130583\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-522272927 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750174938454%7C11%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxCU3I3MmUwQm1XMEEyQnBFTnV5V3c9PSIsInZhbHVlIjoiSzNUVUkzSWltZXhHQ1NXaHRhcExWMkpWMzlDSWZ5TzNxT09RWmIrR3doR212d2tyNHd4bXp0ZEVuWU90TTJVQkQzT3M3UEtqMEtQU2NlMWljK3FuMTNjQldrRUI0VUc5ZlRGV3crdFBtVklhdzA2YnZVbXkvZFpmUlBKQXMzaHJvL0QwZ0dVSEJDdDJjY0xxQmt4aEovQmdPTytRUmxtcXhjR3BCWVVUc3c5ZXNMbkhKV0o5Rm5XSllMeHM1aDhiSzg1Wmh1Q2p4UDZGa1Y2VGtLNVNPMy9WUnlQMkNZUWluWXdUaFhzR3RSV3U4MDZPMnpqT2g0MzJrOWQ0Ulo1Ym9ocWR1RFZFVGxtSGtxTDhKRWtReGtDanlzSFhnenpxVVFSLzlhdjVHblFYMWJyZmNLby9sMllCU2dIVlN1Tk1JemM5WFBuUDl0dTM4MUorYURoM3RtcHZZbFFyZVRPZ3BlOVNWcW5sVFB4a0s0L0kvOG03YTVJbTBwWVNlcGRSdDNNTnY4bmdKczdDV0hhNUM1anRSUEtpNHY3SVhmd0k4UktOZ2h5WURLMVVhRU1XNnBDTFY4K20wSnYzR0c4aWt2ejdocnladUhCc2UxYzU2QjEzUDJwLzAxVGsvUW9wSDNnRm12QXZRR2NKTEJRSWo0dHB0eTRKVXQ3VU5EQzQiLCJtYWMiOiI4ZDQyODJkYTk5ZWQ0MmE0YmIxMGUxZGJhYzRiZGIyYmU1ZjUwYjM5ZDczZWU5OGI0NjQ0NWUzNzUwMWUzZGQ5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IkJyU2JraUxTMzlVVVVLYmM0YnB5WGc9PSIsInZhbHVlIjoic2J2c3VIZ3hOQ2ZaZFppeWZucVlZVkNzMW1MMXg0eXVJVURBL3c0dm5VYmlnVUFiNU9rNnZmL2UrMFpLall4N0ZGbGU0c085bVlOSGErQ0tyZEV4WER4NDZTbmptQXc0NnZFamtjdG45UEVmdUlRUnBWbkpNWVJGWmw4ZktlcERpRnZPUUFvZzV0UXpzY2h4bUtFQmNjQStKY3FvSXNPbXZ4V1NjU3c3QjJpbFRublpCL1cyR2JpRkNFNzEvckFlZXYrR3pBMWVaMXBBSkhWUFdpR0IyaHZJOUFmU2JqTHViK3gyQnI5aHFBdzY3eTg4TlpBS3RCbGtManZHTGQ5TkRsZEZTWEtQZjFYZ0lCdEtKUng2bVR2TnNZa054Uk5VaGRTakczdE9naDgxemQyL2JFVHRaNFQ1ZGZpU3dqS2JaYk8wL0k3RDhLdHpvSzh0NXh2U2REMm8zVDlQMTRyVFNtdUk5eXFiVUVad2dRS3cvOEl2azUvLzdzZFczMEt3T2lUZjlCdDJRT0w2UHJpUFhQVWpndlhDNDZ1SDhDV2pybHB6NzRnc3M3Rmg5Ly9saHFpTDVrc0VOR0dIV2JyTTN3MXNxMzluWHZ2Y3M1UXBKSmZKcGdxeEIrd0hSQnY3aXVtMGtnVExndTZoZU1iamdZWXJpdXNabk1QUXAyRVciLCJtYWMiOiJkNzYzYTExOWU3NjJjMDcwOTY3YWNlYmUyM2Q1YTJkMWM3MDQxMWU5ZWJlZWVhNjUzNzFjOTcwMTY1YWM0ZGM5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-522272927\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1132825537 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1132825537\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1716553083 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:42:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application, X-CSRF-TOKEN</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBrK296aHVnM25vd1V1L3NTc2wyNlE9PSIsInZhbHVlIjoiR1UzbEtqNjliVm1FTXk3MHhpNmhiNDVmSlQ3N01BQ3pUSkU3MnBBdnVwOThXOWttQ3JFZ2QyK0kyeE8wYmVGT05KQ1JtU0UvTlJ3V1NnUTA0RjhqNlovZ2hHUUhraktaR20ydFNIQW5rb0xlTjRVblFyb01BSElLdGVHYU9UYlBqRzl2a25CQjU1NkNiYm9pNk5VZVpZV0t4TDRYKzZxSjY2eUU1QjZzTTJmSHJyS1BLRWJVQnVzSXpPNi9QMFBaa1RJWFdqSzA0N2pnNlpmTUtaUUJnY3JjVGtOa2wwUWJBODl4Tk50Unhzaks5TFBHUG5acWJwazZWSVFLU2kyK1JTUEw4eXh2LzRmTzI1Ly9ObW1GZGdLSTI4UUNCRG5yVjcxOTI1UlI0NUFBM1VzK0EycjFWeW94dk9iMm9iVDFxUVl3cko1OGVadmk0VVBXK2RPNVlmcVkyMllqb01iUnFOKzZtT2dvWHJXWUpJLzAyTjFibWMycmZwdThzeE5FQWhScVNmNFJISFV4UXM0WldxU2pUc3NNcEQvRFF1bVNrU3Uva2twcmIzMmMrZGN3aitKREdxOGFNMkZQRkRFVG43YUhpZzE3TngwczNUekN0YlJmQm50ZmZWUkRwV29yYjVCMkFsSHZMbGhFQU9xc0hEb3g0Z2EybnNnSlJRMXIiLCJtYWMiOiIxNjA5YWI2ZWVkYzI1NDU2YzRjZTIzM2JkYmRmOGJkMjA3YWMxNTFkNGZhNDQ5Y2NjNmY1NjI3NDBiZDQxYzliIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlhGQUVPWmZjejIvZm1pSndYczQvZmc9PSIsInZhbHVlIjoiUSs2aFU5d1NlbktmVFc5VFliMC9UamYvM1Y0bmNPSFZIYS9EdllVQnEwM1g1RFlzMHhEVVM1dzZSUnpscDlMZi93MzlaTWx0SkZMcnJ3THp2cjV6UUU5bDA4OHJ3RUpqbjI1YUgwN1NTSFhhUHB0alZudVc4WEFDWUFTcnBwd0YwTExXQWJWOGpheXF3WUszQ1g0T0lwazU5TkI0REMwZnFkY0xRcXlCMXM2SHZoOHhINURPcHpMMUpva1ArL3ZoTlR4RCtuUnZjTVQxbjQzS3Vza0JkVXVQcmxOSnpUSjlRVmpRRWFEVjA1TzRDNFVFVE16T2J0ekcwN09ndTFBL203Z3NQalRwdFhjUDlQdm1SOTg3cEdIaFBNc0MzS2dhVDRsQU1HLzNicEtVQ2t5QTY4c3hUYkF5RE5lZkRlMERsdU9pRVJxVWhhQk1tUFlGVWhWdUE3WVBnMUVOZ1NlSDNaWmtISTRWa3N1Q09VZTN2cVNEV0xvZEdaYU5wWDlWU0E4Z3lucmVGKzJnd0sycndZU2Y2UmRIdDI3STY2dFhGTnAvYjQ5aEJPeXpvMjB6aHRGV3lkRkpCSFlRczN6SmNqeDJHc1pQOHU5WlJhYkIvN2ZsQllncjZOQ1g2Q0o2eE9DbjNjbDAyZWJDR3lMSkExYkUwUzJSaCtwZDBMWVYiLCJtYWMiOiJmNTU2MmU2NzQ2NGQyMDgzM2M3OGRhNzg4ZGQxNDM4OTU3NDllMTA2MGE3ODcwZjkyZmI3NTIyZmY2ZjdjZDZhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBrK296aHVnM25vd1V1L3NTc2wyNlE9PSIsInZhbHVlIjoiR1UzbEtqNjliVm1FTXk3MHhpNmhiNDVmSlQ3N01BQ3pUSkU3MnBBdnVwOThXOWttQ3JFZ2QyK0kyeE8wYmVGT05KQ1JtU0UvTlJ3V1NnUTA0RjhqNlovZ2hHUUhraktaR20ydFNIQW5rb0xlTjRVblFyb01BSElLdGVHYU9UYlBqRzl2a25CQjU1NkNiYm9pNk5VZVpZV0t4TDRYKzZxSjY2eUU1QjZzTTJmSHJyS1BLRWJVQnVzSXpPNi9QMFBaa1RJWFdqSzA0N2pnNlpmTUtaUUJnY3JjVGtOa2wwUWJBODl4Tk50Unhzaks5TFBHUG5acWJwazZWSVFLU2kyK1JTUEw4eXh2LzRmTzI1Ly9ObW1GZGdLSTI4UUNCRG5yVjcxOTI1UlI0NUFBM1VzK0EycjFWeW94dk9iMm9iVDFxUVl3cko1OGVadmk0VVBXK2RPNVlmcVkyMllqb01iUnFOKzZtT2dvWHJXWUpJLzAyTjFibWMycmZwdThzeE5FQWhScVNmNFJISFV4UXM0WldxU2pUc3NNcEQvRFF1bVNrU3Uva2twcmIzMmMrZGN3aitKREdxOGFNMkZQRkRFVG43YUhpZzE3TngwczNUekN0YlJmQm50ZmZWUkRwV29yYjVCMkFsSHZMbGhFQU9xc0hEb3g0Z2EybnNnSlJRMXIiLCJtYWMiOiIxNjA5YWI2ZWVkYzI1NDU2YzRjZTIzM2JkYmRmOGJkMjA3YWMxNTFkNGZhNDQ5Y2NjNmY1NjI3NDBiZDQxYzliIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlhGQUVPWmZjejIvZm1pSndYczQvZmc9PSIsInZhbHVlIjoiUSs2aFU5d1NlbktmVFc5VFliMC9UamYvM1Y0bmNPSFZIYS9EdllVQnEwM1g1RFlzMHhEVVM1dzZSUnpscDlMZi93MzlaTWx0SkZMcnJ3THp2cjV6UUU5bDA4OHJ3RUpqbjI1YUgwN1NTSFhhUHB0alZudVc4WEFDWUFTcnBwd0YwTExXQWJWOGpheXF3WUszQ1g0T0lwazU5TkI0REMwZnFkY0xRcXlCMXM2SHZoOHhINURPcHpMMUpva1ArL3ZoTlR4RCtuUnZjTVQxbjQzS3Vza0JkVXVQcmxOSnpUSjlRVmpRRWFEVjA1TzRDNFVFVE16T2J0ekcwN09ndTFBL203Z3NQalRwdFhjUDlQdm1SOTg3cEdIaFBNc0MzS2dhVDRsQU1HLzNicEtVQ2t5QTY4c3hUYkF5RE5lZkRlMERsdU9pRVJxVWhhQk1tUFlGVWhWdUE3WVBnMUVOZ1NlSDNaWmtISTRWa3N1Q09VZTN2cVNEV0xvZEdaYU5wWDlWU0E4Z3lucmVGKzJnd0sycndZU2Y2UmRIdDI3STY2dFhGTnAvYjQ5aEJPeXpvMjB6aHRGV3lkRkpCSFlRczN6SmNqeDJHc1pQOHU5WlJhYkIvN2ZsQllncjZOQ1g2Q0o2eE9DbjNjbDAyZWJDR3lMSkExYkUwUzJSaCtwZDBMWVYiLCJtYWMiOiJmNTU2MmU2NzQ2NGQyMDgzM2M3OGRhNzg4ZGQxNDM4OTU3NDllMTA2MGE3ODcwZjkyZmI3NTIyZmY2ZjdjZDZhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1716553083\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-580447888 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-580447888\", {\"maxDepth\":0})</script>\n"}}