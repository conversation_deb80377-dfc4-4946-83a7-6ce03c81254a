{"__meta": {"id": "X40b0da4a0aede7d924fe539c40f7d84d", "datetime": "2025-06-17 15:42:56", "utime": **********.887459, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174975.389317, "end": **********.887501, "duration": 1.4981839656829834, "duration_str": "1.5s", "measures": [{"label": "Booting", "start": 1750174975.389317, "relative_start": 0, "end": **********.731872, "relative_end": **********.731872, "duration": 1.342555046081543, "duration_str": "1.34s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.731895, "relative_start": 1.3425779342651367, "end": **********.887505, "relative_end": 4.0531158447265625e-06, "duration": 0.1556100845336914, "duration_str": "156ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44976936, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02962, "accumulated_duration_str": "29.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.829794, "duration": 0.02789, "duration_str": "27.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.159}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.867193, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 94.159, "width_percent": 5.841}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1821916372 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1821916372\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-587620561 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-587620561\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-926222088 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-926222088\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1040305499 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750174938454%7C11%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFJUldUeWhrdTJrYTlsVDhtcm1aN3c9PSIsInZhbHVlIjoiTDdIYm1sUGJjWXVKL3orNXJzREhCT2d6R0dHSzZ6R0JTcEFISTgrZWR0QnVLWFk5Q0lVejBkR2FrV0xVVVRSL2NRRXhWRkcxSDl0Q0RWam5yYm8wRUlTWkFKL1A4ZzA2MnQ3bzlQOWxFRVFRR0RzdktyV3BSczY5U29TRXJQWEZpWDh6cGZDQ2p6LzQrUHlKMDNKWmNaTFRMbXJGR3h2Y3JkdzZSWXIwLy9wbDMySUxKODlpVzdueXpNemk5MUpKQ3NiVmFZVTN3akErNTFoRGVhU3B2anBNZnZjdkxOWkh2U29MUmNkWjhpd0ZXakZsUkdPbnVqNUQzTWg1c3Nadks4bG0waVhzMDE1dklMWmpwdXllbmwzU3gzcGhLdlVvcFlZMVFPWVl3ZVdOZDBGYnkxT2ZSY3FXMXM3RFJGZHQ0dUx6TUtJY0hIbmJDV0tZOWIrZzJqeFZWUitMOExxenUvc1lCYkxlOHR1YnZTVklZRDVVYXRnUjFWeHROdWhDLy90MmplbTg1L1JvVWpYY2FDMnR0cTZZUWpIM1JBZUlJV0VZQytncjBoWjlqR2czU1dROW9iaXMvOTNYVVJGRENhVHdaNHBEL3p0KzBmdEpGTVh3YysxVUd6bWZuVkVKZHArdTNHYURFZ0pMVXRmRWRPZnhqanNiVWxMNU9ZSU8iLCJtYWMiOiI1MjA0OTRhMGYyNjM2NDNmYTcyN2E4MjViNDc0YTMwNWEwZTUzMDRiOTJkMDY0MzlmMmVkNjE3ZDU0NzUzN2E5IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InViRCsrdlRMK1JLempoUDFEZUZhOGc9PSIsInZhbHVlIjoiT2tlQkRTV3dhbmJjWEZ2ZURJdkZBR2w3aG5tZ0U0TGdKNUsraWdqVXoyZmI4eHpEY29ZRzErZG9VcmMyZ2dQTmtaazNjd25sdmRsUGs4ekRPWnpYUWNYMlRvd0gyTDFTTmV5NG9DTUZEWUo3d24xbnd5QU5tcTZRdnE5cTFsNEhqSGh0RXNaejBNd1hyZkgrbENMMURuRG5VYVdLNkNMMldSaFRDSVBCK2hLaEZOUTF0OThoOTM1NmsxZFFlWEJYYnp0S2MyTHVKMkYxdHowTXVtL1NCUlZaWXc3TFdlejIxSERoSTB6Q2swblkrelJVRGhPdlJVRHVVMEd1enhqbTJ0R1RBOXZqek1VcUUrYVh1QW5tVWIwZXQ0dnFmODEyWFNqdWxZU1VvTCtlelR6eGlsQWdQS2Qzb0FJRTZlMkNOemw0eEZrZHdKOVlTWk41UmMxTWVHaUVhRHBVM3o5VUJNVC9FeEhoKzBralB5VWJmZFZiOHUvUVVET09STHhOaHhGS2Y0WlEyWVlrcUJLR0lwR3dRQmNjQnVkRTRXN2JqeEJpTFNtczJEbDdUeW9IdHNheEZlWXh4ekxpeis4b2g5YUg5K2pCVXRmKy90elpaOHZ0WVFpNEZMcUMweEMrRWJ1Vmc5d3YxRTBEblNuTlBacFdGWGF3RVdFeVFHbVgiLCJtYWMiOiJkNTQwYmMzNjg1ZTU5YzQ5ODExMDg4NDliMDVjMjM2MjAxZjEyYjAyZWU0ZTBjMDAwNTc4YjkxYWQxZWNmYWY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1040305499\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1315707857 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1315707857\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-381655839 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:42:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFzT3lFYVZiYlZnTWZwMm5kZjVaalE9PSIsInZhbHVlIjoiK3RzOXErQmN4TURMUGp6QmxlNmRYVGhqL2ZDQk1UOUNQekRYNVozcW5zbVl0Vy9nYjZTU2xjYzFtTmwrR1NmQm83c2lJWEdkWFltOWg1K0l0a28vY3hnbndPVlV4N0pYUTF3eFhEQ3A2VHd2Lzl6RVh2LytydTdpZStoR3dlNmh2NjYyRE9aZEhwdEJlaDlCSmMzenl2cnViTjVkTzdtY1M4MlNiNkdvb3FncXNveFZDNVRRUWUxMlBGWlZtTUtIelowaytoSkYwMU8vZFZoUzdGVEFEcXdvZzZQQUEySXFCWE83aUowcXVteUxBNWJENXVRSEJybVFCYWFlZWVBc1hKWUlVMGtXTWZSYncxK1JvdTlDQndhRXY1R1FsYUs2YjIrQ0p2S1F1d05UcDlGeGFsNGkyQmptU05lRUpsMisvKzdSdWxXeU1BN1BuMVZYL2U2WnhuWkpjUnM0M0lxWFVsTkVJOGdCc2lDZys2eVhNd0xFWHVVZmtTL0h0RXcrZTJYWWg5bHhONkszSEFiRFY1SUNQSkRzZ1FKZldtRnd3bm5OWEkwQ1FBNG1iQ0daOEhCbUsveFpHWStCVmNaNEdVelRuako1cGJWMHBZM3p1blk5YWx0U0luS1lON2VDNHpWME95TjV0S05lRHpsdFd4NVlmMzEySDkvYS81RlMiLCJtYWMiOiJmMWNjMzA2OTRlODE2YzNjOTQyZDY5OWJkMDljZGM2NGFkNWM5YmI5ZmIyZDgwMmVkOWEwODJjYWI5NTcwMzJiIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IncrY1FTYmNLeHF6UUdXRXBza3NzOFE9PSIsInZhbHVlIjoiS00zaHVkZVNHbjJtM1FWSjJQRXhFYzNIM0o5SzRhRTZrWUlUcmJHaWllS1BKRHA2alBZY00zZEY4TXRXNHFCS3BLL05TNm5oY3NaQkFVUzRDSUhrQU9yNDliNStYZmhkNDNuWCszZnBKL0NLRDRDMGJFbEJnQzlqS3UrdDk4bVZ2cjFBMkxDMGhsS2FsQ2g0UkNUV2NSdFVRemVtNHpsbGN5V21ScTJDSEdWUVJiZkZKRlB3RGoremxOUkdsU2ErYk1uUGJ4bzlzN003U2lDS05hc2RhZTVjOWI5SFdRK0tiRW41OVlOWXNjbzZDdEtCcEVCN1EwUlRJT1Y4OUFLUkZSSTlLZ1hoWFZ3cFg3OUxxNyswdVlPc0JQeUZzc003K3RCVUFTQ0Zqa0xZcExxYUZKWHh5cHFzZEo4ZG1UaHNGN3V0di9WNmNUQnIrQXF6bFJldUpya2U2WG41b3h5MHZHVEQydE41MGRRTFdDWkVKWFg5ZCtjaGFZYlViZ1UzMXNuMWVRZStjaFBJSTMzRm93bloxV1lBeWhmRDZaUm5YRW9FZ3EzUXNSelNWUG4wZzNDREZ0UHNMUkpQd3ZuZXlKZnlIRzNzek9oeDhiWlZtdE1IemdaSXlIUzdVdUZQZVNsNFVnVlJkQzVNV3pOaExrUlVRbm5wTEJlUkwwd2kiLCJtYWMiOiI2MmM2ZWYzYjZjN2M0MjRkMWFlMjEyZWZmNzIwY2ZhYWM3MTkzMDc0ZDdhYmEzOTBkNWExYmVhMmU3ZTdhNTFjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFzT3lFYVZiYlZnTWZwMm5kZjVaalE9PSIsInZhbHVlIjoiK3RzOXErQmN4TURMUGp6QmxlNmRYVGhqL2ZDQk1UOUNQekRYNVozcW5zbVl0Vy9nYjZTU2xjYzFtTmwrR1NmQm83c2lJWEdkWFltOWg1K0l0a28vY3hnbndPVlV4N0pYUTF3eFhEQ3A2VHd2Lzl6RVh2LytydTdpZStoR3dlNmh2NjYyRE9aZEhwdEJlaDlCSmMzenl2cnViTjVkTzdtY1M4MlNiNkdvb3FncXNveFZDNVRRUWUxMlBGWlZtTUtIelowaytoSkYwMU8vZFZoUzdGVEFEcXdvZzZQQUEySXFCWE83aUowcXVteUxBNWJENXVRSEJybVFCYWFlZWVBc1hKWUlVMGtXTWZSYncxK1JvdTlDQndhRXY1R1FsYUs2YjIrQ0p2S1F1d05UcDlGeGFsNGkyQmptU05lRUpsMisvKzdSdWxXeU1BN1BuMVZYL2U2WnhuWkpjUnM0M0lxWFVsTkVJOGdCc2lDZys2eVhNd0xFWHVVZmtTL0h0RXcrZTJYWWg5bHhONkszSEFiRFY1SUNQSkRzZ1FKZldtRnd3bm5OWEkwQ1FBNG1iQ0daOEhCbUsveFpHWStCVmNaNEdVelRuako1cGJWMHBZM3p1blk5YWx0U0luS1lON2VDNHpWME95TjV0S05lRHpsdFd4NVlmMzEySDkvYS81RlMiLCJtYWMiOiJmMWNjMzA2OTRlODE2YzNjOTQyZDY5OWJkMDljZGM2NGFkNWM5YmI5ZmIyZDgwMmVkOWEwODJjYWI5NTcwMzJiIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IncrY1FTYmNLeHF6UUdXRXBza3NzOFE9PSIsInZhbHVlIjoiS00zaHVkZVNHbjJtM1FWSjJQRXhFYzNIM0o5SzRhRTZrWUlUcmJHaWllS1BKRHA2alBZY00zZEY4TXRXNHFCS3BLL05TNm5oY3NaQkFVUzRDSUhrQU9yNDliNStYZmhkNDNuWCszZnBKL0NLRDRDMGJFbEJnQzlqS3UrdDk4bVZ2cjFBMkxDMGhsS2FsQ2g0UkNUV2NSdFVRemVtNHpsbGN5V21ScTJDSEdWUVJiZkZKRlB3RGoremxOUkdsU2ErYk1uUGJ4bzlzN003U2lDS05hc2RhZTVjOWI5SFdRK0tiRW41OVlOWXNjbzZDdEtCcEVCN1EwUlRJT1Y4OUFLUkZSSTlLZ1hoWFZ3cFg3OUxxNyswdVlPc0JQeUZzc003K3RCVUFTQ0Zqa0xZcExxYUZKWHh5cHFzZEo4ZG1UaHNGN3V0di9WNmNUQnIrQXF6bFJldUpya2U2WG41b3h5MHZHVEQydE41MGRRTFdDWkVKWFg5ZCtjaGFZYlViZ1UzMXNuMWVRZStjaFBJSTMzRm93bloxV1lBeWhmRDZaUm5YRW9FZ3EzUXNSelNWUG4wZzNDREZ0UHNMUkpQd3ZuZXlKZnlIRzNzek9oeDhiWlZtdE1IemdaSXlIUzdVdUZQZVNsNFVnVlJkQzVNV3pOaExrUlVRbm5wTEJlUkwwd2kiLCJtYWMiOiI2MmM2ZWYzYjZjN2M0MjRkMWFlMjEyZWZmNzIwY2ZhYWM3MTkzMDc0ZDdhYmEzOTBkNWExYmVhMmU3ZTdhNTFjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-381655839\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-579416781 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-579416781\", {\"maxDepth\":0})</script>\n"}}