{"__meta": {"id": "X97e412f4cadd57edc9a4646bcf2d3532", "datetime": "2025-06-17 15:42:53", "utime": **********.348947, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174971.867581, "end": **********.348984, "duration": 1.481403112411499, "duration_str": "1.48s", "measures": [{"label": "Booting", "start": 1750174971.867581, "relative_start": 0, "end": **********.028624, "relative_end": **********.028624, "duration": 1.1610431671142578, "duration_str": "1.16s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.02865, "relative_start": 1.1610691547393799, "end": **********.348988, "relative_end": 4.0531158447265625e-06, "duration": 0.32033801078796387, "duration_str": "320ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49205496, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.038740000000000004, "accumulated_duration_str": "38.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1637492, "duration": 0.02608, "duration_str": "26.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.321}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.21767, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.321, "width_percent": 2.943}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.274113, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 70.263, "width_percent": 4.724}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.282472, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 74.987, "width_percent": 4.13}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3016088, "duration": 0.0044599999999999996, "duration_str": "4.46ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 79.117, "width_percent": 11.513}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3158681, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 90.63, "width_percent": 9.37}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-641167620 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-641167620\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.297958, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1670240264 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1670240264\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-830333667 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-830333667\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-391601977 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-391601977\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-567061678 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750174938454%7C11%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxQT1BzQ3F4aTY3V01SU2ZuVzd6Y0E9PSIsInZhbHVlIjoidDZleWUyZzluYU15WXFyb2IvWmJocXFzNTlwZWtlMWZCYXprUnRaNVFNNUhhK1RtSTFBRlpKSCt0Ri90Nk9YQmdad2oxaXEyenBoRCsreUV1aUZKajFCcUVyazY0cHhqNDZHOXI0TWRqc1FjQWhRWmNPVm5YM21zTnhQVTZVdVh3czA2TzRBNitWTW9qS05KNzEzRWxoMHBKdnpRWTVOVldxQlgxdzY0eUlpSVZkeUdhRGs4TnJ1WUZNSG9jVWFpSkYzc0tCZTJ2S2xIR3VBNElrWUxMcE1aMFErbmFPeEJmZUpMK05hZjdvdkpUTXkxVGpjMHI1Q2p3MW51SUF3Q25CY1h3V0hpQm9WZnBncFR1MEhRR2NJaHAvdmdISlVic0xFNnhLLzJ2VTh6OEtmN1BKZUxWS0lLL3U4bFR3YVdSTjNXSWJ5ZHAyejBJS3Vxb3haMWlzYWlEa1piWkFXWUNJd0tHTlhTbWVUVTducmRQM0w2QTJKR1FBNVYzZkF6ckZwd3A5VWNOUlh0WEZ2WDlOeTI4V2tIVlVDOUhVeFlZM3lwdVlBY2ZZcnk1b2hlNW9URyt5SXZWdm9zZURsaTRoUmdub3U0TkxKejFCVyswRFBxZjA4MXIvdzRnSk5qSUpRaWw3clpNQWxSZE5CMzRDbDhDQ2Z0N01ISmV3RmIiLCJtYWMiOiIwMjJhNWZlZDZjMTQzMDQwYWY1OWU3ZmJlZDIyNGE1ZjdlZmNiYjg5ZTljNjc2MWE2YmQ4MGQxYWZiYzU3NTlmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IlpZQ0tMd2l0ekN1RzQ1Z2ZwOFZmckE9PSIsInZhbHVlIjoiQVI4MXFBNHZ1Wkg1ZXJmOEp5cVdtRWZ1YnRaRWpNUjNGNnF3NGQxaXR3R2tjRkRrZ2lIYi9VSkw1QTNsTzFxTzhicU1lOW5DOWF4TVBhM3FkMzZpcTIwUElIbVErcHRVYjMxczFwVXhDaHRJcEQ4ZEE5MERmZWZmVWFNMnRyNkEwam9sRXFOZXpLc2lRc0REU2ppaG9ZVUNLT3pvQ3RYNWJqbUEyYjMyM1M3dUVRa0tmaERJMmt5SnEwTVFIaFpaRk91U0dNK1VpNjR5VjVMQTRCLzRTVmc4eG4wMkFtcFN3WW4zdXAzQnBTTG83MEU2VDdwRTVGRG9yN3J0bm5QZGcvMVVSR3REczExODdyeU1qNmdlNTUwcFZMT1VqOEg5NnVaNXV2UUFPeFV5T21JcjQ3VGNHSEhmYUVyV2hpUFdhUjZ3NkZTbHc3anpSZUs1emtkK2ZrK0JxZG90aTZ0RWl2VDNzVDZpbFVEcnNYam9xMzZpQmo4MzRwdm1hRUY1ZFI2T1I5ZWtyWHBsSWtPdVNwempkejJaZnhrVHhWYk5MV0p5YmdRN2JiZFVGY2szRlVUQVN3OHM3ZWJvUnNab1FjY29tMjY0ZlFybFIxcEJrWURVV0pKYTBOS0JXQ0hWamR2Tmlqc1pvcXRGWC9NUzJWL09TUjMxNzZZbW91dUgiLCJtYWMiOiIyNjUwOTA4ODk0YWQ5YTJmMzJlYTMzZGRkNzM1NmNjOWI3MWVjMDk2ZWY4MjdmMjAwNTQ0OTQzM2M0MDliYWVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-567061678\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-283709329 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-283709329\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1313870534 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:42:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imd2ZkhwNlZVWGhZOVNONnIzUURyWlE9PSIsInZhbHVlIjoiVTRScWNXTnlyL2xOYlZXSWE5cnRFSEt1eE9oSVZuYXJGaSthZTZOVDRaUDA0ZktyVE1qdzVmaUtGcmk4ZEM1Q01ua3l1NVd2R1lWZThqWVBxaUtGY2VYYXBFc3NvZjlicm0vZlgvSzBncUErR3c0TDlwZHZ5UHpOUUUzdS9KVWJFTW0yQnNwamltL2FSalJHTHBYV1RMVC8veG9yRU11MEFuVmFoa1BhUXh1cnVKMVptYkNnT204WnlXUUlLOHVZdmZrZjdzYTRyUkZUay8wSUo5QnlIM1NIdWZYRXNGRnpuNUZPcFIwOHFIOTQ1Tkl2Sk0va3cwRlpkYzd1YUpOMHNISFE1ZUloOFNYaXdKZ2RaRUhYV2t2YVE5OFNxYmhZUW45NDhDOGtjK2daM0d4dDFMNTc2SGk3a0t1VVRDQ0ZBSVNlSFBveVhpR1p1c2ZqeFhuSHdnUFBzTUl5ais5dVp6THoyc1BtQ3lObUVZNGpiUEdZRDRJOVptUXBKcEUyQUhNS2MrWHJtNXdhR0laQmpSeXNZVmRLVzdBeTBSTDV6SU9xangzdE1PUEZYUzVzV2Jwckh6eTBueVNSTXROdGl5blhGTkhqNlFqWTNkTGpsQzhQMTR2U29GaVN3bWFvbDVWTitQd1JaL3FuSXg1NkhlZkUvZnFkc0FYaWVnZFAiLCJtYWMiOiIxMGMzODVjYjY1ZDBjOTlhMjc4ZmE5YzllNjA4Mzc4YzQyM2M4YjdkMjk2MzJlYmEyNDBmNzVkZDIyODc0ZjE0IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlFWd3JFbVl4cjY0VWN4Zm9kbXUxN0E9PSIsInZhbHVlIjoienZLMEk5TmVCUlI3RjdiQnBoL1dJSU0xMHpoNWsvY0tEdk9jZkIrb1ROY3B6UTdWVlNudk1uZmd4WG1OK29vb0dlMVd6YlgxK0dpa25WTXdudFZjRGg3NkJmcXVYTEFXbkdFb3JTajR5Y1FOdHBIVzhCVjQzWmNsaVdKbkVZT05JRjE3eGQvZ25GTHVCWWZieW1xendTZERMNlEwcExsVk92WmQ5cEZuU3dwNjRTZ3o2aEFhNEhTKzhsbzR4U0I2dFZYZ2Vrb1c1UDhMa1lkU2Vqd0JBNGU0Tk5PRzk2K1BneVBMcklCV3Q5SzlNK3lCK0FJZXFpcnRtMlNKRC8zQk1IYStKUnRvdC9US2NTK0N4bUdhTlQ5a0hjRHlhUCtheUdDd3FiS0I3U1dYWmdXT1NFa3hHRXU4eU44eHUxb0xwZUVIbTB1QWUxbU1jL1NQcE01aVBleENuSUhMVTlhUllPdWFFalUvdFRmUUhmTjVWdEI3TDhIYzNvZS9ocjhxK2VheTlMbkd3MGJDbVFFRzlFMEpGOE0yZVhHODlBRERTMC91UFVxQUNxSUZ0QXFVNklmSFhkOU92aEhwc0VjUGpiVlE5ZytGK1pYalJJeEdhYzFMQi90VmlOSkVwRjdKZVBnbVVDbVBpL3dRUmZaZkRqSUFtVWM5Y2NMQ2hEZksiLCJtYWMiOiI3MzM1MDQ0OThiNjk1NTU5NzU0NGRkNDI0NzQ2NmY0NGUxOWI1M2EyODE4NmVmNTEyNGNkZGE4Njc3YWY2NWU5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imd2ZkhwNlZVWGhZOVNONnIzUURyWlE9PSIsInZhbHVlIjoiVTRScWNXTnlyL2xOYlZXSWE5cnRFSEt1eE9oSVZuYXJGaSthZTZOVDRaUDA0ZktyVE1qdzVmaUtGcmk4ZEM1Q01ua3l1NVd2R1lWZThqWVBxaUtGY2VYYXBFc3NvZjlicm0vZlgvSzBncUErR3c0TDlwZHZ5UHpOUUUzdS9KVWJFTW0yQnNwamltL2FSalJHTHBYV1RMVC8veG9yRU11MEFuVmFoa1BhUXh1cnVKMVptYkNnT204WnlXUUlLOHVZdmZrZjdzYTRyUkZUay8wSUo5QnlIM1NIdWZYRXNGRnpuNUZPcFIwOHFIOTQ1Tkl2Sk0va3cwRlpkYzd1YUpOMHNISFE1ZUloOFNYaXdKZ2RaRUhYV2t2YVE5OFNxYmhZUW45NDhDOGtjK2daM0d4dDFMNTc2SGk3a0t1VVRDQ0ZBSVNlSFBveVhpR1p1c2ZqeFhuSHdnUFBzTUl5ais5dVp6THoyc1BtQ3lObUVZNGpiUEdZRDRJOVptUXBKcEUyQUhNS2MrWHJtNXdhR0laQmpSeXNZVmRLVzdBeTBSTDV6SU9xangzdE1PUEZYUzVzV2Jwckh6eTBueVNSTXROdGl5blhGTkhqNlFqWTNkTGpsQzhQMTR2U29GaVN3bWFvbDVWTitQd1JaL3FuSXg1NkhlZkUvZnFkc0FYaWVnZFAiLCJtYWMiOiIxMGMzODVjYjY1ZDBjOTlhMjc4ZmE5YzllNjA4Mzc4YzQyM2M4YjdkMjk2MzJlYmEyNDBmNzVkZDIyODc0ZjE0IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlFWd3JFbVl4cjY0VWN4Zm9kbXUxN0E9PSIsInZhbHVlIjoienZLMEk5TmVCUlI3RjdiQnBoL1dJSU0xMHpoNWsvY0tEdk9jZkIrb1ROY3B6UTdWVlNudk1uZmd4WG1OK29vb0dlMVd6YlgxK0dpa25WTXdudFZjRGg3NkJmcXVYTEFXbkdFb3JTajR5Y1FOdHBIVzhCVjQzWmNsaVdKbkVZT05JRjE3eGQvZ25GTHVCWWZieW1xendTZERMNlEwcExsVk92WmQ5cEZuU3dwNjRTZ3o2aEFhNEhTKzhsbzR4U0I2dFZYZ2Vrb1c1UDhMa1lkU2Vqd0JBNGU0Tk5PRzk2K1BneVBMcklCV3Q5SzlNK3lCK0FJZXFpcnRtMlNKRC8zQk1IYStKUnRvdC9US2NTK0N4bUdhTlQ5a0hjRHlhUCtheUdDd3FiS0I3U1dYWmdXT1NFa3hHRXU4eU44eHUxb0xwZUVIbTB1QWUxbU1jL1NQcE01aVBleENuSUhMVTlhUllPdWFFalUvdFRmUUhmTjVWdEI3TDhIYzNvZS9ocjhxK2VheTlMbkd3MGJDbVFFRzlFMEpGOE0yZVhHODlBRERTMC91UFVxQUNxSUZ0QXFVNklmSFhkOU92aEhwc0VjUGpiVlE5ZytGK1pYalJJeEdhYzFMQi90VmlOSkVwRjdKZVBnbVVDbVBpL3dRUmZaZkRqSUFtVWM5Y2NMQ2hEZksiLCJtYWMiOiI3MzM1MDQ0OThiNjk1NTU5NzU0NGRkNDI0NzQ2NmY0NGUxOWI1M2EyODE4NmVmNTEyNGNkZGE4Njc3YWY2NWU5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1313870534\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-730329581 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-730329581\", {\"maxDepth\":0})</script>\n"}}