{"__meta": {"id": "X08969cd22e8b01380d5a4d88d6c552b5", "datetime": "2025-06-17 15:42:45", "utime": **********.685484, "method": "GET", "uri": "/pos-payment-type?vc_name=6&user_id=&warehouse_name=8&quotation_id=0", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174964.353536, "end": **********.685516, "duration": 1.3319802284240723, "duration_str": "1.33s", "measures": [{"label": "Booting", "start": 1750174964.353536, "relative_start": 0, "end": **********.444646, "relative_end": **********.444646, "duration": 1.0911099910736084, "duration_str": "1.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.444666, "relative_start": 1.091130018234253, "end": **********.685519, "relative_end": 2.86102294921875e-06, "duration": 0.24085307121276855, "duration_str": "241ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49355728, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PosController@posBillType", "namespace": null, "prefix": "", "where": [], "as": "pos.billtype", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FPosController.php&line=1516\" onclick=\"\">app/Http/Controllers/PosController.php:1516-1624</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02058, "accumulated_duration_str": "20.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5562952, "duration": 0.01607, "duration_str": "16.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 78.086}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.59693, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 78.086, "width_percent": 5.637}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.6476328, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 83.722, "width_percent": 7.92}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.655646, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 91.642, "width_percent": 8.358}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1868437778 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1868437778\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.667926, "xdebug_link": null}]}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-722582416 data-indent-pad=\"  \"><span class=sf-dump-num>404</span>\n</pre><script>Sfdump(\"sf-dump-722582416\", {\"maxDepth\":0})</script>\n", "status_text": "Not Found", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-410402033 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-410402033\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1667431146 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1667431146\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1690114505 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750174938454%7C11%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1DRHUrcVk1WDAvQXFYYU9TdE9pR0E9PSIsInZhbHVlIjoidk1VWHE1WkJFbk0zWHBlYzRlamdUejh4VkRBbHhuOGk1a3dIRkhBbUoxM1Z5MXVSdDRKVDQ1bnR6OHlFZ3ZrL3F2Q1JsR0VCeC9Md2lXVFBQbjlBeXc2R2ZncnE1bXhkblk4NlVCeWpiMTZYTVRHRWdBTjM4ajUySmFiL0JhZ3VlMGhURHRqVkl0dHkvQi9ZUXVTNFBHWStoOEpaa0h2bmtpT1NWVFgvRlA4eUlSaEJWZVE3VXNSTjNUTkNuRDBSRlVEQTVpaFhMR3ZKOXNVSW11a3NSdERtZms0bW1Hcmk0bHFuSHhwcHhabWZFamM2SmN6d09Reks1eWo3WmpOMG4yQTdQRC8xckhMZU5keHdhTktmVnJWbkZDdkVqbVFFYUxGaUFrMnJ6ZmNzKzNWSkpYS0NTWDEyL0x5YTltZkdGRFRXRCtpMkdyVnhuQ3NZU2RLcWZYSWQwUTFDSERyVGVwdHFVU2Q0WDVoTWhpcmZMMjg2RHA1OE5WL3UvWm4raHk0UDRvTmRURjNTc1JsTXVBYzBGTk8zSlRSMk5PckZxY2t3TWZmV09NZmpEcXY1OFpyUGU3KzQxeWZHbURJdlhacEt5ZERFUFZkS2tLaW9UNWJNcmEwd3pzWGhHaEJ5M1FDRzBLd3FjaDB5QVB6MFNKQlJPTTVKNFNnNUhMenoiLCJtYWMiOiJmYThmMjE1NDQzMjZlYmZjMTYxYmJmZmI0OTQyYWUzZjI3MjMyYzVhNGQ3NDYwOTgwNTZiYjU4YzA3MjkzYTYyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InNLUVlZRzBxbE5OWGs1MGYzd0FIQWc9PSIsInZhbHVlIjoiakZxRkNBTEg4NE9DR0hORkJZVEhiM09ZejhTNlNFTXYyMTlkSW1yVEJMUWs2aHRleGJJcGhCLytNeC9lZVM3cnJ1TFpKaGFPR0RGMlpQVjZyTzB1Vk1Ub2RuVitxR1Y1bE5xWC93RzVGSGh0RUJ6RnNrK0JRT0VmYWJiQ0U5bjd3OVd0c2tNWFJZYVdGdG5ZNUlLdGdJeFNxUzRhZ0ZveHVkQWRqS0E5em41UnJGS1VVV2RnOHFQVzN2Zkt3NGhzQXdVK01vUXVrTWVlZm1uVnJlMURzL0VIUUZhci9UZEF5akJGaEYzc0RjRlVMTS83djlwenljOXMzaDBldkhzZjFuanA3eWZiVHNJdmZ5TExndTZEbFJ3NHRheU4rbGZibm9VKzNaNjVheEx5ZGcrMmFEaWVUV05KOFZQbGtqTk5RY09CaHF0dlUyVXpCSzR5S2pJdWRpMWxzRGdGeXpHWkpPR2RjRkp3ZDF2WEU1ckdUeW85ZjZQcTNKQlNrTWVVaVpnVUFDcWtPWXRtaXY1WjdSbGpnSWxaMnlXRUZhQW55L2VudE92TkNCWC82VnNGcTR5QUx4M0gxKzdBVXpNdlNiTVpsM2FrUEtzSDd4QWxHNGJ2R281cEw0S1VpTlVWWWsyYlcwdjFCMkpqMlJ4cm5qUlR3RzRJRDZRMjRhRTMiLCJtYWMiOiIxYmE4NDZmODQ3MGY2NWM4ZmE5YmJmODM2Y2QyYzhhOWQzOGVmMDY3ZGQ0MWNmMDc4MTg0OThhN2ZiODY3YWNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1690114505\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1580962234 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1580962234\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-933339487 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:42:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRjWGEwb2h0NVhpRFBtSGlGVVoyU1E9PSIsInZhbHVlIjoic1dwMndBWmxoZzZHZnlzL1VvdnFWQWY1UjY2cXB2TmlHUnR6TlVIVTk2QXY2MU5yVTJPaCs4SGczazdObzZYTUdOVGV3azZBeDJaRDYwYSszMjhmS0YxRnYvcHBlZVE5cEY5NTU5WmpBSWErNlVuanMwUHNBcDZYNFB3RDkvMkFVZTZpSVc1VGtvcjVPVmNNeEI1aEsvRG1LRXVKalRINEVkbUZ3bk9odVFiMnZDczRwU3U0OVg5OXk0SHo3b1ZZUmwxeVBNbXNyYWRQbk9KdTBBODdjNHNyMlFqVVRsdjNpUlBBdWc1d1pHNWVWOHlUanBycFZNS1hMZHlxUjdHNWtUTmdmQWNWSXNlUXNzRU9EdzVPS1JBU3EwVWFTN29uN2l2YmQ3OHJVOFp3ODhoTElxSFluWXJQVzBjU2MyemFWa1lRTlNkL0RlTlhGSXlEL05DQUM3V2tObG40aWlCdFVIZnkxbE4zSnluRm1hb09MM2pvb0twYmJCY1krQXhmNlFmM2RhZjZjZko4aGNiMzE5eDdqeXRuN0dnOVFacmwxRzduMEhBVldpcms3cDFsZ2tBbTBOekdQS0R5R2twY2pTSFJOM212S2J2VTVCbC9mc3lLMitENTJxVGRYQTJrUGtkSjdUVXNLdC90WE5DenVnVU1iR0ZPMTBCVUs1ZVQiLCJtYWMiOiIwMmEwNGU3OTk3NmMxMzg4N2QwMjViYjFjNjJiN2E3Y2I4YmM4NDI2MzA2Yzc5MDRiODA2OTdhMjE2MWUzYjNmIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IllnTjBuUk50dXV4T0plNCswKzlUMWc9PSIsInZhbHVlIjoiMTBVOXYzdmRvMFBOOWs0V1NVaXlaK2JWMzZ5bkYweGdONnNWR0lTSGxWaEtPNDFEamZ1MW5QdnZNVnZCTXhnUS92RFpQMmFva1FXYllvMGtvdnVscHlkRnQvYjZrSTNXeWFOL0YvNFFUN0xCQk1xc3VGYTliSlBJcGlqbStYT1QxYm9vdmM4WjhQMGZ1eVd4TWJhSnlhYnFrNzNWWEt1dURqT3ZLTkZHUVR6OVpBUVJuTHllRENydS9QNUFUejZrMm1xR2t4SmVreDZDb2Q2WTNFSThaTU9sWTdaamlwcFZtY3lRQjd3cTBkN3FnTXNvREVXaWljZEtnL2tWNi8wSmtwZkFsbzdSQ21TN21Kc0tjUVJsMFhyTm5pbGgzdXhQaGYyZ0FsMHU0RHJDbXVVT2RqUVFhS0w4VGljcG1DK1ZWY2xtZm5QNGtEcWphZjVhT1NLWUtZTDl6Z1V0SFFhQVVrdkp2bURaMXlhQXBmWUhDajJZUFRtYkt4SVdMY2hYdEltREsydlNGbFI5aVJzT2tGNW9iNnl2eG53aS9DMXo4bkVQODhvWXU0OVIwclJVS09Cd2c5Wk5KVVRIM1RYalN2RmJMYk1hM20xa1EyUWxDTXpOUTVhMG00S1JZcDM1UXFnVEdGRVBDSVJRSDY2VzFJSmwzM2RpaTZkeFZoM2YiLCJtYWMiOiIxMjk3ZDBlMmNjODM2NjhkMjAyMGEyNmM5MDZhOTVlNjE0ZWJiZmYyNmI5NjI4NmZkNGRlYjQ3YTk5ZmY1NjZkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRjWGEwb2h0NVhpRFBtSGlGVVoyU1E9PSIsInZhbHVlIjoic1dwMndBWmxoZzZHZnlzL1VvdnFWQWY1UjY2cXB2TmlHUnR6TlVIVTk2QXY2MU5yVTJPaCs4SGczazdObzZYTUdOVGV3azZBeDJaRDYwYSszMjhmS0YxRnYvcHBlZVE5cEY5NTU5WmpBSWErNlVuanMwUHNBcDZYNFB3RDkvMkFVZTZpSVc1VGtvcjVPVmNNeEI1aEsvRG1LRXVKalRINEVkbUZ3bk9odVFiMnZDczRwU3U0OVg5OXk0SHo3b1ZZUmwxeVBNbXNyYWRQbk9KdTBBODdjNHNyMlFqVVRsdjNpUlBBdWc1d1pHNWVWOHlUanBycFZNS1hMZHlxUjdHNWtUTmdmQWNWSXNlUXNzRU9EdzVPS1JBU3EwVWFTN29uN2l2YmQ3OHJVOFp3ODhoTElxSFluWXJQVzBjU2MyemFWa1lRTlNkL0RlTlhGSXlEL05DQUM3V2tObG40aWlCdFVIZnkxbE4zSnluRm1hb09MM2pvb0twYmJCY1krQXhmNlFmM2RhZjZjZko4aGNiMzE5eDdqeXRuN0dnOVFacmwxRzduMEhBVldpcms3cDFsZ2tBbTBOekdQS0R5R2twY2pTSFJOM212S2J2VTVCbC9mc3lLMitENTJxVGRYQTJrUGtkSjdUVXNLdC90WE5DenVnVU1iR0ZPMTBCVUs1ZVQiLCJtYWMiOiIwMmEwNGU3OTk3NmMxMzg4N2QwMjViYjFjNjJiN2E3Y2I4YmM4NDI2MzA2Yzc5MDRiODA2OTdhMjE2MWUzYjNmIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IllnTjBuUk50dXV4T0plNCswKzlUMWc9PSIsInZhbHVlIjoiMTBVOXYzdmRvMFBOOWs0V1NVaXlaK2JWMzZ5bkYweGdONnNWR0lTSGxWaEtPNDFEamZ1MW5QdnZNVnZCTXhnUS92RFpQMmFva1FXYllvMGtvdnVscHlkRnQvYjZrSTNXeWFOL0YvNFFUN0xCQk1xc3VGYTliSlBJcGlqbStYT1QxYm9vdmM4WjhQMGZ1eVd4TWJhSnlhYnFrNzNWWEt1dURqT3ZLTkZHUVR6OVpBUVJuTHllRENydS9QNUFUejZrMm1xR2t4SmVreDZDb2Q2WTNFSThaTU9sWTdaamlwcFZtY3lRQjd3cTBkN3FnTXNvREVXaWljZEtnL2tWNi8wSmtwZkFsbzdSQ21TN21Kc0tjUVJsMFhyTm5pbGgzdXhQaGYyZ0FsMHU0RHJDbXVVT2RqUVFhS0w4VGljcG1DK1ZWY2xtZm5QNGtEcWphZjVhT1NLWUtZTDl6Z1V0SFFhQVVrdkp2bURaMXlhQXBmWUhDajJZUFRtYkt4SVdMY2hYdEltREsydlNGbFI5aVJzT2tGNW9iNnl2eG53aS9DMXo4bkVQODhvWXU0OVIwclJVS09Cd2c5Wk5KVVRIM1RYalN2RmJMYk1hM20xa1EyUWxDTXpOUTVhMG00S1JZcDM1UXFnVEdGRVBDSVJRSDY2VzFJSmwzM2RpaTZkeFZoM2YiLCJtYWMiOiIxMjk3ZDBlMmNjODM2NjhkMjAyMGEyNmM5MDZhOTVlNjE0ZWJiZmYyNmI5NjI4NmZkNGRlYjQ3YTk5ZmY1NjZkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-933339487\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-574982169 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-574982169\", {\"maxDepth\":0})</script>\n"}}