{"__meta": {"id": "Xb458b2ff5b0a44badb5ef87279f084d5", "datetime": "2025-06-17 15:42:37", "utime": **********.708565, "method": "GET", "uri": "/customer/check/delivery?customer_id=6", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750174956.196013, "end": **********.708607, "duration": 1.5125939846038818, "duration_str": "1.51s", "measures": [{"label": "Booting", "start": 1750174956.196013, "relative_start": 0, "end": **********.527272, "relative_end": **********.527272, "duration": 1.33125901222229, "duration_str": "1.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.527295, "relative_start": 1.331282138824463, "end": **********.708611, "relative_end": 4.0531158447265625e-06, "duration": 0.18131589889526367, "duration_str": "181ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44962072, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02461, "accumulated_duration_str": "24.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.630874, "duration": 0.02323, "duration_str": "23.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 94.393}, {"sql": "select * from `customers` where `customers`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\مجلد جديد\\ppp\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.673636, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "ty", "start_percent": 94.393, "width_percent": 5.607}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2F%D9%85%D8%AC%D9%84%D8%AF%20%D8%AC%D8%AF%D9%8A%D8%AF%2Fppp%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-125011588 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-125011588\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-572689309 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-572689309\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-840535429 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-840535429\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1681083323 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">_clck=2gyfy%7C2%7Cfwu%7C0%7C1994; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1dam46h%7C1750174938454%7C11%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxqUXRxaHI2WS9ZMTFKb09lZHZXa3c9PSIsInZhbHVlIjoiSHEyYlI4cHZ4UWRIaVJtTEI2WnF6WmdmL3h5SzFxYnpjaTdySm8rbVB0UC83VzJ3L2diOFVIV01XYnRQaEUzS2J0RGNMaTRiNTFlL2ptdkthdUZHWERxbGd1aGRFelkweGFiVnh3b1RVeVhSdHNQSkFEOTBBbmU5bXdFWGE0Zk1MN1FBcCsxZW5ZUnFUV2pTK0J4b3hHdzdCYlB4SGdBUUt6NVhEdzJkM0cwZHN2bkx5UEc3MEJ6aFVkT3FTNTVaUkprUGx2dXdWSC81bU5SVi9vMTFRd041S1Z0bTJyMU5JOXBIZCtwZkN2NTZaVENzTXJIR2NGZlNrWjNWWHlDRjNxdGVJa3Q5Q1c5SEFvZ2YrdDYxaExiNy9pdUtma0M0VnhKODdOM3JVdFlFbEhBc3lPWkN6SW1xRmk3M3JBenFVTG03NHJ0QUQ2UTZhV2l1V2ZTTENTZGFML1hhcitiOEdtQXpPOTZDWEI0ckZ1UW15V1lUUmVsMWNpT21QdjBKV2pHQUpFMUFIRk5HVU1pdXlpOEp4T1dmRmdzSWpRTzV2dk9zcXRGSjlSRHpTWFBPMDJkMjJhM0xFOHVzOTBuOVFvb2p4WDErNVRsU2w3Q0xIdEpnTFlXMmVBazJhMmlVdTZPT2ozaVhCUWQ5cE44Q1JndnpBV2NxRHFoeCsxSmIiLCJtYWMiOiI3N2JmYWQwMTRiZDUwZTNmMDRkMTcwOTE1NmY1ZjViOWE0MzljYjI1ZTE1YjRlMmNjNDA2MjUyMDEwOGVhM2NmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InNEeXJJVHJiWnlYSHNZQVRqQi9oeVE9PSIsInZhbHVlIjoiYUVSUU53QkdKc3MvdUhUeVNQVk02Q3JNcWtDRWJZRGtJVVUxMG9ocUtrSGh5Q0xxYzlUY0llSXFRamFDSVlYK2hGTWo3M2ZrNEhyZzlIM2N0c1dlUmdIYjhVV0xRUkppaXZaTFNueHBjVGptT2Izbk42UmZHenlYRnhiZkNWcitST0hOVUEveXFXcm9zMEdlYmZpV0F2SjB3SUk5Uko3N3BySUJIb0x4YjdSYlUwUlpwTEJham5XWlRrcWg0SHcvOEhTK0VYV0hjb3R3Yk1LbGpqTDBnRGpHYmdERHlJR0o2dVB5amdzY2xudGNZMmZHOUNLTXZxOVlGUWNiY0o1LzNPb3BqeGRWei9iV3VTaHhiQ3ZyL2NwQndMWXVmZjNONDFLSERmd2Nwc1h6L1Q5Q0Yra01xbk5WdFFKd3doWVQxNEJHTjhNejBBYzd3bjFLcUdsdytEWjNrUDZwU3NEeXk5d3RmcDhaVFBLNCtYMUp0YTk1RnlSVEt1dkhERTBBSVZFU1N4cXNwVzM5UW1BbFNHMUtuSDU3RGE3dmQ0T0tYbmkyMzlxVFFBWDNGUnFYVTl1V1p3bEQ5dUdZYkFKRjA5b1VJK0FDOHFSZmZTUmgwWXpkUXRvazNJazZjeXprcFBRaHJyUk90NFRhT0xWcmVqcHJTOUFnTEVqazY4WDMiLCJtYWMiOiI3ZTg5ZDAxYWU3YTY1MWYyODAzOGMyOWM2MmM1YjI4MDI5NGEzZDdlZjdiY2ZjZjMwYTk3ZGRlYjk3ZWNmZjhjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1681083323\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1955432655 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VloaUQCBjpDKrel7nHeAzB1sTLdTMwteSos1MmAN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1955432655\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2101658705 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 15:42:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlZdXRPUXJuUnM5V0xXOWFoN2lzUlE9PSIsInZhbHVlIjoiMDBFSnI0Q2VUWlE1WjdNOFViZ0h1UFRGeDNNRExwUFR5MmNwZDV2UHlvaVpKbjlWM1lQVWhTL1cvWUpwZ0c5WmFacFlYZHpjZWVzUi9kSHNEV0QrWTF5eGk1S3lwUnR5ZlVEWUlHeDd0L3N1Tzk0dDI0UUh4clhsWEVTTStzV20vRUpZZWlhcDZPcDF3TlFoQXhtMDB6dmVuS3lpNEVaWDlpY0tTNGhSb0J4OFlrQnpGbTc1ZnN1NlJXOGNvWjF1NnZud2dWdTI3TFJxS2Jza2p0LytmT0x0YkhKYlFjUjJuVE1UNnBUNCtkV1dnRUhKVW8wSzU4Qy91RUdrQjRXVVNHM2xnNXZjRDRsSGt3SFE0RG9PYmtlK2ltUU5IcWQzQ1lURCtFTDVNelhFU3NJa3N2K1FDamsvMExLR2ZEQm80TTd1NWErbXVvVEozRjhXRy9GdStoVTNtOXJDYXMxcTF6L3hhd1J4RTRlWThzZ3lIWkRLVVdDK0VXd2wwUEFpazl5UGNtN2M2eTlQSEl6TEJGNytRZTNxdWNqZm0zQk1vSzllMU5XbW1OdFRUcnNBdmJtdHFUWlZtZ3JoRnUvSVF1Um01bjNrS0ZBSitpU3lod1RPY0xkWmxveDhvN2dLNWRXMk5HU3lyOEJMSUF1OTZGU0NqSlc0cW15ZWxFOVQiLCJtYWMiOiJhODJlMmVhN2E1OTc5NDAzYTM0YTljMDhjYWZkYTU3Y2FiMThkNDU0MTA4MjIwZGVmZTYyOWI3ZTRkOGI3NDk4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImUyWkRaK0U5clJQMkpVUTgreGFiWlE9PSIsInZhbHVlIjoieFR4RFhaMWhjbytjOEltWG9oajNNdHRtOWE4b25UazZRa05hZjdDNU51ZGpJeE52aUNBYVNDcVdtQ1NhMnZOV1MyNXdOeFN4NXlGTW1paXNZNlZvQ0g2YUxPNkhLdG5MUXEya2RjcXBBTGkzMEs5anlDa1I2Q2JXRmFpaXhRM0RqSjI2OC8xUUR4UDNpbzhab0JuRkNIMGNjUTJKUkV5cm5VcHNpS2tXRnNZK1RTT00rT2V5MlA0dU9WbXYrWXo5Tjc2Y1JOcmpSRmdEV1VLeXJhZVJubGhwV0lidGpDYXpBRk4xckJ4VEZsQWlXc3JPVjlyRVNUR1NjMlIyMEc3Y29tcWRZeDU3RWZWaWp0NDhxQzRJb2Z3cnFPRDdkYUpWUmJncXBlYmkvb1NlZGc3UW9nZHV3S1lRczlMT1ZWM2xBOXhhbkNnMFZuenA2dFB6K3pRc1NxYUYzZUw2R2plWkFQakxiVW1zTkU2R0RsUW9RL01ranVCaHZrQStKVGx0TjNoUVlpMXFYYUJPQ0lCYXdPRTFSb25lTUs4Mk1ZVDFySDhOU2g5K2hiODdJdnFtZ28xdGVHbUp6YzhLblBTaWU2cVh0V2JSWWRYeE0wdDdpbVBNQzZxb3lUREZKSXViVlcrNHNRTnlKaTcwcEc1SmU3dnRTd0ZXeTRTWHNKeisiLCJtYWMiOiI0N2FlNDM4ZTJiYTU0ZTE3NDU2MmU1MTc0YmRkMzVmZjA0ODYyNWM1MzhkNTY1MGI0NmMzYWY5Y2JkNWZlNDg2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 17:42:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlZdXRPUXJuUnM5V0xXOWFoN2lzUlE9PSIsInZhbHVlIjoiMDBFSnI0Q2VUWlE1WjdNOFViZ0h1UFRGeDNNRExwUFR5MmNwZDV2UHlvaVpKbjlWM1lQVWhTL1cvWUpwZ0c5WmFacFlYZHpjZWVzUi9kSHNEV0QrWTF5eGk1S3lwUnR5ZlVEWUlHeDd0L3N1Tzk0dDI0UUh4clhsWEVTTStzV20vRUpZZWlhcDZPcDF3TlFoQXhtMDB6dmVuS3lpNEVaWDlpY0tTNGhSb0J4OFlrQnpGbTc1ZnN1NlJXOGNvWjF1NnZud2dWdTI3TFJxS2Jza2p0LytmT0x0YkhKYlFjUjJuVE1UNnBUNCtkV1dnRUhKVW8wSzU4Qy91RUdrQjRXVVNHM2xnNXZjRDRsSGt3SFE0RG9PYmtlK2ltUU5IcWQzQ1lURCtFTDVNelhFU3NJa3N2K1FDamsvMExLR2ZEQm80TTd1NWErbXVvVEozRjhXRy9GdStoVTNtOXJDYXMxcTF6L3hhd1J4RTRlWThzZ3lIWkRLVVdDK0VXd2wwUEFpazl5UGNtN2M2eTlQSEl6TEJGNytRZTNxdWNqZm0zQk1vSzllMU5XbW1OdFRUcnNBdmJtdHFUWlZtZ3JoRnUvSVF1Um01bjNrS0ZBSitpU3lod1RPY0xkWmxveDhvN2dLNWRXMk5HU3lyOEJMSUF1OTZGU0NqSlc0cW15ZWxFOVQiLCJtYWMiOiJhODJlMmVhN2E1OTc5NDAzYTM0YTljMDhjYWZkYTU3Y2FiMThkNDU0MTA4MjIwZGVmZTYyOWI3ZTRkOGI3NDk4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImUyWkRaK0U5clJQMkpVUTgreGFiWlE9PSIsInZhbHVlIjoieFR4RFhaMWhjbytjOEltWG9oajNNdHRtOWE4b25UazZRa05hZjdDNU51ZGpJeE52aUNBYVNDcVdtQ1NhMnZOV1MyNXdOeFN4NXlGTW1paXNZNlZvQ0g2YUxPNkhLdG5MUXEya2RjcXBBTGkzMEs5anlDa1I2Q2JXRmFpaXhRM0RqSjI2OC8xUUR4UDNpbzhab0JuRkNIMGNjUTJKUkV5cm5VcHNpS2tXRnNZK1RTT00rT2V5MlA0dU9WbXYrWXo5Tjc2Y1JOcmpSRmdEV1VLeXJhZVJubGhwV0lidGpDYXpBRk4xckJ4VEZsQWlXc3JPVjlyRVNUR1NjMlIyMEc3Y29tcWRZeDU3RWZWaWp0NDhxQzRJb2Z3cnFPRDdkYUpWUmJncXBlYmkvb1NlZGc3UW9nZHV3S1lRczlMT1ZWM2xBOXhhbkNnMFZuenA2dFB6K3pRc1NxYUYzZUw2R2plWkFQakxiVW1zTkU2R0RsUW9RL01ranVCaHZrQStKVGx0TjNoUVlpMXFYYUJPQ0lCYXdPRTFSb25lTUs4Mk1ZVDFySDhOU2g5K2hiODdJdnFtZ28xdGVHbUp6YzhLblBTaWU2cVh0V2JSWWRYeE0wdDdpbVBNQzZxb3lUREZKSXViVlcrNHNRTnlKaTcwcEc1SmU3dnRTd0ZXeTRTWHNKeisiLCJtYWMiOiI0N2FlNDM4ZTJiYTU0ZTE3NDU2MmU1MTc0YmRkMzVmZjA0ODYyNWM1MzhkNTY1MGI0NmMzYWY5Y2JkNWZlNDg2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 17:42:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2101658705\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1203264138 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PbUz36JtXNH0QnevVEFVUYD6Fik0AD5p4sOat9ZK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1203264138\", {\"maxDepth\":0})</script>\n"}}